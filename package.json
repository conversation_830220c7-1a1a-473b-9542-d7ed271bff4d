{"name": "uniapp-vite-template", "version": "0.0.1", "license": "MIT", "engines": {"node": "20"}, "scripts": {"build:app": "cross-env VITE_MODE=production uni build -p app", "build:app-android": "cross-env VITE_MODE=production uni build -p app-android", "build:app-android:dev": "cross-env VITE_MODE=development uni build -p app-android", "build:app-android:prod": "npm run build:app-android", "build:app-android:staging": "cross-env VITE_MODE=staging uni build -p app-android", "build:app-ios": "cross-env VITE_MODE=production uni build -p app-ios", "build:app-ios:dev": "cross-env VITE_MODE=development uni build -p app-ios", "build:app-ios:prod": "npm run build:app-ios", "build:app-ios:staging": "cross-env VITE_MODE=staging uni build -p app-ios", "build:app:dev": "cross-env VITE_MODE=development uni build -p app", "build:app:prod": "npm run build:app", "build:app:staging": "cross-env VITE_MODE=staging uni build -p app", "build:h5": "cross-env VITE_MODE=production uni build", "build:h5:dev": "cross-env VITE_MODE=development uni build", "build:h5:prod": "npm run build:h5", "build:h5:ssr": "cross-env VITE_MODE=production uni build --ssr", "build:h5:ssr:dev": "cross-env VITE_MODE=development uni build --ssr", "build:h5:ssr:prod": "npm run build:h5:ssr", "build:h5:ssr:staging": "cross-env VITE_MODE=staging uni build --ssr", "build:h5:staging": "cross-env VITE_MODE=staging uni build", "build:mp-alipay": "cross-env VITE_MODE=production uni build -p mp-alipay", "build:mp-alipay:dev": "cross-env VITE_MODE=development uni build -p mp-alipay", "build:mp-alipay:prod": "npm run build:mp-alipay", "build:mp-alipay:staging": "cross-env VITE_MODE=staging uni build -p mp-alipay", "build:mp-baidu": "cross-env VITE_MODE=production uni build -p mp-baidu", "build:mp-baidu:dev": "cross-env VITE_MODE=development uni build -p mp-baidu", "build:mp-baidu:prod": "npm run build:mp-baidu", "build:mp-baidu:staging": "cross-env VITE_MODE=staging uni build -p mp-baidu", "build:mp-jd": "cross-env VITE_MODE=production uni build -p mp-jd", "build:mp-jd:dev": "cross-env VITE_MODE=development uni build -p mp-jd", "build:mp-jd:prod": "npm run build:mp-jd", "build:mp-jd:staging": "cross-env VITE_MODE=staging uni build -p mp-jd", "build:mp-kuaishou": "cross-env VITE_MODE=production uni build -p mp-kuaishou", "build:mp-kuaishou:dev": "cross-env VITE_MODE=development uni build -p mp-kuaishou", "build:mp-kuaishou:prod": "npm run build:mp-kua<PERSON>ou", "build:mp-kuaishou:staging": "cross-env VITE_MODE=staging uni build -p mp-kuaishou", "build:mp-lark": "cross-env VITE_MODE=production uni build -p mp-lark", "build:mp-lark:dev": "cross-env VITE_MODE=development uni build -p mp-lark", "build:mp-lark:prod": "npm run build:mp-lark", "build:mp-lark:staging": "cross-env VITE_MODE=staging uni build -p mp-lark", "build:mp-qq": "cross-env VITE_MODE=production uni build -p mp-qq", "build:mp-qq:dev": "cross-env VITE_MODE=development uni build -p mp-qq", "build:mp-qq:prod": "npm run build:mp-qq", "build:mp-qq:staging": "cross-env VITE_MODE=staging uni build -p mp-qq", "build:mp-toutiao": "cross-env VITE_MODE=production uni build -p mp-toutiao", "build:mp-toutiao:dev": "cross-env VITE_MODE=development uni build -p mp-toutiao", "build:mp-toutiao:prod": "npm run build:mp-to<PERSON><PERSON>", "build:mp-toutiao:staging": "cross-env VITE_MODE=staging uni build -p mp-toutiao", "build:mp-weixin": "cross-env VITE_MODE=production uni build -p mp-weixin", "build:mp-weixin:dev": "cross-env VITE_MODE=development uni build -p mp-weixin", "build:mp-weixin:prod": "npm run build:mp-weixin", "build:mp-weixin:staging": "cross-env VITE_MODE=staging uni build -p mp-weixin", "build:quickapp-webview": "cross-env VITE_MODE=production uni build -p quickapp-webview", "build:quickapp-webview-huawei": "cross-env VITE_MODE=production uni build -p quickapp-webview-huawei", "build:quickapp-webview-huawei:dev": "cross-env VITE_MODE=development uni build -p quickapp-webview-huawei", "build:quickapp-webview-huawei:prod": "npm run build:quickapp-webview-huawei", "build:quickapp-webview-huawei:staging": "cross-env VITE_MODE=staging uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "cross-env VITE_MODE=production uni build -p quickapp-webview-union", "build:quickapp-webview-union:dev": "cross-env VITE_MODE=development uni build -p quickapp-webview-union", "build:quickapp-webview-union:prod": "npm run build:quickapp-webview-union", "build:quickapp-webview-union:staging": "cross-env VITE_MODE=staging uni build -p quickapp-webview-union", "build:quickapp-webview:dev": "cross-env VITE_MODE=development uni build -p quickapp-webview", "build:quickapp-webview:prod": "npm run build:quickapp-webview", "build:quickapp-webview:staging": "cross-env VITE_MODE=staging uni build -p quickapp-webview", "dev:app": "cross-env VITE_MODE=development uni -p app", "dev:app-android": "cross-env VITE_MODE=development uni -p app-android", "dev:app-android:dev": "npm run dev:app-android", "dev:app-android:prod": "cross-env VITE_MODE=production uni -p app-android", "dev:app-android:staging": "cross-env VITE_MODE=staging uni -p app-android", "dev:app-ios": "cross-env VITE_MODE=development uni -p app-ios", "dev:app-ios:dev": "npm run dev:app-ios", "dev:app-ios:prod": "cross-env VITE_MODE=production uni -p app-ios", "dev:app-ios:staging": "cross-env VITE_MODE=staging uni -p app-ios", "dev:app:dev": "npm run dev:app", "dev:app:prod": "cross-env VITE_MODE=production uni -p app", "dev:app:staging": "cross-env VITE_MODE=staging uni -p app", "dev:h5": "cross-env VITE_MODE=development uni", "dev:h5:dev": "npm run dev:h5", "dev:h5:prod": "cross-env VITE_MODE=production uni", "dev:h5:ssr": "cross-env VITE_MODE=development uni --ssr", "dev:h5:ssr:dev": "npm run dev:h5:ssr", "dev:h5:ssr:prod": "cross-env VITE_MODE=production uni --ssr", "dev:h5:ssr:staging": "cross-env VITE_MODE=staging uni --ssr", "dev:h5:staging": "cross-env VITE_MODE=staging uni", "dev:mp-alipay": "cross-env VITE_MODE=development uni -p mp-alipay", "dev:mp-alipay:dev": "npm run dev:mp-alipay", "dev:mp-alipay:prod": "cross-env VITE_MODE=production uni -p mp-alipay", "dev:mp-alipay:staging": "cross-env VITE_MODE=staging uni -p mp-alipay", "dev:mp-baidu": "cross-env VITE_MODE=development uni -p mp-baidu", "dev:mp-baidu:dev": "npm run dev:mp-baidu", "dev:mp-baidu:prod": "cross-env VITE_MODE=production uni -p mp-baidu", "dev:mp-baidu:staging": "cross-env VITE_MODE=staging uni -p mp-baidu", "dev:mp-jd": "cross-env VITE_MODE=development uni -p mp-jd", "dev:mp-jd:dev": "npm run dev:mp-jd", "dev:mp-jd:prod": "cross-env VITE_MODE=production uni -p mp-jd", "dev:mp-jd:staging": "cross-env VITE_MODE=staging uni -p mp-jd", "dev:mp-kuaishou": "cross-env VITE_MODE=development uni -p mp-kuaishou", "dev:mp-kuaishou:dev": "npm run dev:mp-kua<PERSON><PERSON>", "dev:mp-kuaishou:prod": "cross-env VITE_MODE=production uni -p mp-kuaishou", "dev:mp-kuaishou:staging": "cross-env VITE_MODE=staging uni -p mp-kuaishou", "dev:mp-lark": "cross-env VITE_MODE=development uni -p mp-lark", "dev:mp-lark:dev": "npm run dev:mp-lark", "dev:mp-lark:prod": "cross-env VITE_MODE=production uni -p mp-lark", "dev:mp-lark:staging": "cross-env VITE_MODE=staging uni -p mp-lark", "dev:mp-qq": "cross-env VITE_MODE=development uni -p mp-qq", "dev:mp-qq:dev": "npm run dev:mp-qq", "dev:mp-qq:prod": "cross-env VITE_MODE=production uni -p mp-qq", "dev:mp-qq:staging": "cross-env VITE_MODE=staging uni -p mp-qq", "dev:mp-toutiao": "cross-env VITE_MODE=development uni -p mp-toutiao", "dev:mp-toutiao:dev": "npm run dev:mp-to<PERSON><PERSON>", "dev:mp-toutiao:prod": "cross-env VITE_MODE=production uni -p mp-toutiao", "dev:mp-toutiao:staging": "cross-env VITE_MODE=staging uni -p mp-toutiao", "dev:mp-weixin": "cross-env VITE_MODE=development uni -p mp-weixin", "dev:mp-weixin:dev": "npm run dev:mp-weixin", "dev:mp-weixin:prod": "cross-env VITE_MODE=production uni -p mp-weixin", "dev:mp-weixin:staging": "cross-env VITE_MODE=staging uni -p mp-weixin", "dev:quickapp-webview": "cross-env VITE_MODE=development uni -p quickapp-webview", "dev:quickapp-webview-huawei": "cross-env VITE_MODE=development uni -p quickapp-webview-huawei", "dev:quickapp-webview-huawei:dev": "npm run dev:quickapp-webview-huawei", "dev:quickapp-webview-huawei:prod": "cross-env VITE_MODE=production uni -p quickapp-webview-huawei", "dev:quickapp-webview-huawei:staging": "cross-env VITE_MODE=staging uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "cross-env VITE_MODE=development uni -p quickapp-webview-union", "dev:quickapp-webview-union:dev": "npm run dev:quickapp-webview-union", "dev:quickapp-webview-union:prod": "cross-env VITE_MODE=production uni -p quickapp-webview-union", "dev:quickapp-webview-union:staging": "cross-env VITE_MODE=staging uni -p quickapp-webview-union", "dev:quickapp-webview:dev": "npm run dev:quickapp-webview", "dev:quickapp-webview:prod": "cross-env VITE_MODE=production uni -p quickapp-webview", "dev:quickapp-webview:staging": "cross-env VITE_MODE=staging uni -p quickapp-webview", "check:types": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "check:deps": "taze -f", "postinstall": "npx simple-git-hooks", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:lint-staged": "lint-staged", "lint:style": "stylelint --cache \"**/*.{vue,scss,css}\" --fix", "commit": "git pull && git add -A && git-cz && git push"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@dcloudio/uni-ui": "^1.5.7", "@tanstack/vue-query": "4.37.1", "@uni-helper/uni-network": "^0.20.0", "@uni-helper/uni-promises": "^0.2.1", "@uni-helper/uni-use": "^0.19.14", "@vueuse/core": "12.7.0", "change-case": "4.1.2", "core-js": "^3.40.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "pinia": "2.0.36", "qs": "6.5.3", "vue": "3.5.13", "wot-design-uni": "^1.9.1", "z-paging": "^2.8.6"}, "devDependencies": {"@antfu/eslint-config": "^4.3.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/uni-uts-v1": "3.0.0-4050520250307001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@iconify/json": "^2.2.309", "@mini-types/alipay": "^3.0.14", "@types/node": "^20.17.19", "@types/qs": "^6.9.18", "@uni-helper/eslint-config": "^0.4.0", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/uni-env": "^0.1.7", "@uni-helper/uni-types": "1.0.0-alpha.4", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.7", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@uni-helper/volar-service-uni-pages": "^0.2.28", "@uni-ku/root": "^1.2.0", "@unocss/eslint-config": "^66.0.0", "@unocss/preset-rem-to-px": "^66.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/runtime-core": "3.5.13", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "cz-git": "^1.11.0", "eslint": "9.20.1", "lint-staged": "^15.4.3", "miniprogram-api-typings": "^4.0.5", "pinia-plugin-unistorage": "^0.1.2", "postcss-gap-properties": "^6.0.0", "sass": "~1.78.0", "simple-git-hooks": "^2.11.1", "stylelint": "^16.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-order": "^6.0.4", "taze": "^18.6.0", "type-fest": "^4.35.0", "typescript": "5.7.3", "unocss": "^66.0.0", "unplugin-auto-import": "^0.19.0", "unplugin-icons": "^0.22.0", "unplugin-vue-macros": "^2.14.2", "vite": "~5.2.8", "vite-plugin-uni-polyfill": "^0.1.0", "vue-i18n": "^9.14.2", "vue-tsc": "2.2.2"}, "simple-git-hooks": {"pre-commit": "pnpm lint:lint-staged", "commit-msg": "npx commitlint --edit ${1}"}, "config": {"commitizen": {"path": "node_modules/cz-git", "useEmoji": true}}}