import type { ConfigEnv } from 'vite'
import process from 'node:process'
import postcssGapProperties from 'postcss-gap-properties'
import { defineConfig, loadEnv } from 'vite'
import { createViteProxy } from './build/config'
import { setupVitePlugins } from './build/plugins'
import { convertEnv, getRootPath, getSrcPath } from './build/utils'

// https://vitejs.dev/config/
export default defineConfig(async (configEnv: ConfigEnv) => {
  const srcPath = getSrcPath()
  const rootPath = getRootPath()
  const viteEnv = convertEnv(loadEnv(configEnv.mode, process.cwd()))
  const { VITE_PORT, VITE_USE_PROXY, VITE_PROXY_TYPE } = viteEnv
  const define: Record<string, any> = {}
  // if (!['mp-weixin', 'h5', 'web'].includes(process.env.UNI_PLATFORM)) {
  define.global = null
  define.wx = 'uni'
  // }
  return {
    plugins: await setupVitePlugins(),
    server: {
      host: '0.0.0.0',
      port: VITE_PORT,
      open: false,
      proxy: createViteProxy(VITE_USE_PROXY, VITE_PROXY_TYPE as ProxyType),
    },
    envPrefix: ['VITE_', 'UNI_'],
    build: {
      target: 'es6',
      cssTarget: 'chrome61',
      reportCompressedSize: false,
      sourcemap: false,
      chunkSizeWarningLimit: 1024, // chunk 大小警告的限制（单位kb）
      commonjsOptions: {
        ignoreTryCatch: false,
      },
    },
    optimizeDeps: {
      exclude: ['vue-demi'],
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: `@import '@/styles/variables.scss';@import '@/styles/wot-design.scss';`,
        },
      },
      postcss: {
        plugins: [postcssGapProperties() as any],
      },
    },
    resolve: {
      alias: {
        '~': rootPath,
        '@': srcPath,
      },
    },
  }
})
