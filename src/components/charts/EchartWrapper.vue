<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, watch, nextTick, useAttrs } from 'vue'
import { echarts } from '@/plugins/echarts'
import type { EChartsOption } from 'echarts/types/dist/shared'

const props = defineProps({
  options: {
    type: Object as () => EChartsOption,
    required: true,
  },
  height: {
    type: String,
    default: '300px',
  },
  width: {
    type: String,
    default: '100%',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  theme: {
    type: String,
    default: '',
  },
})

defineOptions({
  inheritAttrs: false,
})

const emit = defineEmits(['init', 'update', 'click'])
const chartRef = ref<any>(null)
const chartInstance = ref<any>(null)
const attrs = useAttrs()

async function initChart() {
  if (chartRef.value) {
    chartInstance.value = await chartRef.value.init(echarts, props.theme)
    updateChart()
    bindEvents()
    emit('init', chartInstance.value)
  }
}

function updateChart() {
  if (!chartInstance.value) return

  chartInstance.value.setOption(props.options, true)

  nextTick(() => {
    chartInstance.value?.resize()
  })

  emit('update', chartInstance.value)
}

function showLoading() {
  if (chartInstance.value) {
    chartInstance.value.showLoading({
      text: '加载中...',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      textColor: '#909399',
    })
  }
}

function hideLoading() {
  if (chartInstance.value) {
    chartInstance.value.hideLoading()
  }
}

function bindEvents() {
  if (chartInstance.value) {
    chartInstance.value.on('click', (params: any) => {
      emit('click', params)
    })
  }
}

watch(
  () => props.options,
  () => {
    updateChart()
  },
  { deep: true },
)

watch(
  () => props.loading,
  (newVal) => {
    if (newVal) {
      showLoading()
    } else {
      hideLoading()
    }
  },
)

onMounted(() => {
  uni.onWindowResize(handleWindowResize)
})

function handleWindowResize() {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

defineExpose({
  getChartInstance: () => chartInstance.value,
})
</script>

<template>
  <view :class="['echart-wrapper', attrs.class]" :style="{ height: props.height, width: props.width }">
    <l-echart
      ref="chartRef"
      :is-disable-scroll="true"
      @finished="initChart"
      :style="{ height: '100%', width: '100%' }"
    ></l-echart>
  </view>
</template>

<style scoped lang="scss">
.echart-wrapper {
  position: relative;
}
</style>
