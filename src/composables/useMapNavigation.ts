import { getSystemInfo, openLocation } from '@uni-helper/uni-promises'
import { useToast } from 'wot-design-uni'

export interface LocationInfo {
  latitude?: string | number
  longitude?: string | number
  name?: string
  address?: string
}

interface MapAppInfo {
  name: string
  url: string
}

const BAIDU_MAP = {
  name: '百度地图',
  packageName: 'com.baidu.BaiduMap',
  action: 'baidumap://',
  getAndroidURL: (lat: number, lon: number) =>
    `bdapp://map/geocoder?location=${lat},${lon}&src=andr.baidu.openAPIdemo&coord_type=gcj02`,
  getIOSURL: (lat: number, lon: number) =>
    `baidumap://map/geocoder?location=${lat},${lon}&coord_type=gcj02&src=ios.baidu.openAPIdemo`,
}

const AMAP = {
  name: '高德地图',
  packageName: 'com.autonavi.minimap',
  action: 'amapuri://',
  getAndroidURL: (lat: number, lon: number) =>
    `androidamap://viewMap?sourceApplication=softname&lat=${lat}&lon=${lon}&dev=0`,
  getIOSURL: (lat: number, lon: number, name: string) =>
    `iosamap://viewMap?sourceApplication=softname&lat=${lat}&lon=${lon}&dev=0&poiname=${encodeURIComponent(
      name,
    )}`,
}

export function useMapNavigation() {
  const toast = useToast()

  const openAppURL = (url: string) => {
    plus.runtime.openURL(url, (err) => {
      console.error('openURL error:', err)
      toast.show('请先安装指定应用或应用打开失败')
    })
  }

  const handleAndroidNavigation = (lat: number, lon: number) => {
    const availableMaps: MapAppInfo[] = []
    if (
      plus.runtime.isApplicationExist({
        pname: BAIDU_MAP.packageName,
        action: BAIDU_MAP.action,
      })
    ) {
      availableMaps.push({
        name: BAIDU_MAP.name,
        url: BAIDU_MAP.getAndroidURL(lat, lon),
      })
    }
    if (
      plus.runtime.isApplicationExist({
        pname: AMAP.packageName,
        action: AMAP.action,
      })
    ) {
      availableMaps.push({
        name: AMAP.name,
        url: AMAP.getAndroidURL(lat, lon),
      })
    }

    if (availableMaps.length === 0) {
      toast.show('请先安装百度地图或高德地图')
      return
    }

    if (availableMaps.length === 1) {
      plus.runtime.openURL(availableMaps[0].url)
      return
    }

    plus.nativeUI.actionSheet(
      {
        title: '选择地图应用',
        cancel: '取消',
        buttons: availableMaps.map((app) => ({ title: app.name })),
      },
      (e) => {
        if (e.index > 0) {
          plus.runtime.openURL(availableMaps[e.index - 1].url)
        }
      },
    )
  }

  const handleIOSNavigation = (lat: number, lon: number, name: string) => {
    const mapApps = [
      { name: BAIDU_MAP.name, url: BAIDU_MAP.getIOSURL(lat, lon) },
      { name: AMAP.name, url: AMAP.getIOSURL(lat, lon, name) },
    ]

    plus.nativeUI.actionSheet(
      {
        title: '选择地图应用',
        cancel: '取消',
        buttons: mapApps.map((app) => ({ title: app.name })),
      },
      (e) => {
        if (e.index > 0) {
          openAppURL(mapApps[e.index - 1].url)
        }
      },
    )
  }

  const openMapNavigation = async (item: LocationInfo) => {
    const { latitude, longitude, name = '电站位置', address = '' } = item
    if (!longitude || !latitude) {
      toast.show('电站位置信息不完整')
      return
    }

    const targetLatitude = Number(latitude)
    const targetLongitude = Number(longitude)

    // #ifndef APP-PLUS
    try {
      await openLocation({
        longitude: targetLongitude,
        latitude: targetLatitude,
        name,
        address,
      })
    } catch (error) {
      console.error('openLocation error:', error)
      toast.show('打开地图失败')
    }
    // #endif

    // #ifdef APP-PLUS
    const systemInfo = await getSystemInfo()
    if (systemInfo.platform === 'android') {
      handleAndroidNavigation(targetLatitude, targetLongitude)
    } else if (systemInfo.platform === 'ios') {
      handleIOSNavigation(targetLatitude, targetLongitude, name)
    }
    // #endif
  }

  return {
    openMapNavigation,
  }
}
