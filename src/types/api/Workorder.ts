export interface WorkOrderPageParams {
  /** 审核方式：NONE-无需审核，SUB_CENTER-分中心审核，HEADQUARTER-总部二审 */
  auditMode?: string
  /** 运维商业务类型:1--户用,2--工商业,3--户+工 */
  businessType?: number
  /** 市ID */
  cityId?: number
  /** 关闭时间结束 */
  closeTimeEnd?: string
  /** 关闭时间开始 */
  closeTimeStart?: string
  /** 创建时间结束 */
  createTimeEnd?: string
  /** 创建时间开始 */
  createTimeStart?: string
  /** 截止时间结束 */
  deadlineEnd?: string
  /** 截止时间开始 */
  deadlineStart?: string
  /** 下发来源/下发单位：SUB_CENTER-分中心，HEADQUARTER-总部 */
  dispatchAuditPermission?: string
  /** 下发方式：AUTO-自动，REVIEW-审核 */
  dispatchMode?: string
  /** 故障级别 */
  faultLevel?: string
  /** 处理人 */
  handler?: string
  /** 处理人id */
  handlerId?: string
  /** 逆变器SN码 */
  inverterSn?: string
  /** 运维商会员id */
  opMemberId?: number
  /** 运维商名称 */
  opName?: string
  /** 运维商类别:建站服务商--STATION_SERVICE,三方运维服务商--THIRD_SERVICE */
  opType?: string
  /** 工单编号 */
  orderCode?: string
  /** 工单来源 */
  orderSource?: string
  /** 工单状态 */
  orderStatus?: string
  /** 工单状态列表 */
  orderStatusList?: string[]
  /** 工单类型 */
  orderType?: string
  /** 是否超期 */
  overTime?: boolean
  /** 页码 */
  pageNum?: number
  /** 一页数据量 */
  pageSize?: number
  /** 省ID */
  provinceId?: number
  /** 区ID */
  regionId?: number
  /** 备件审批状态:WAIT_AUDIT-待审核,AUDIT_OK-审核通过,AUDIT_REJECT-审核驳回 */
  sparePartAuditStatus?: string
  /** 关联资方 */
  specialFlag?: string
  /** 电站编码 */
  stationCode?: string
  /** 电站名称 */
  stationName?: string
  /** 分中心代码 */
  subCenterCode?: string
}

/** 工单检查项实体类 */
export interface WorkOrderConfigCheckItem {
  /** 检查项 */
  checkItem?: string
  /** 工单配置ID */
  configId?: number
  /** 创建时间 */
  createdAt?: string
  /** 示例图片路径 */
  exampleImage?: string
  /** 检查项ID */
  id?: number
  /** 回填结果类型：text-文本，select-选择，img-图片 */
  resultType?: string
  /** 排序序号 */
  sort?: number
  /** 修改时间 */
  updatedAt?: string
}

export interface WorkOrderFaultInfo {
  /** 创建时间 */
  createdAt?: string
  /** 创建人 */
  createdBy?: string
  /** 故障代码 */
  faultCode?: string
  /** 故障描述 */
  faultDesc?: string
  /** 故障详情 */
  faultDetails?: string
  /** 故障ID */
  faultId?: number
  /** 故障等级 */
  faultLevel?: string
  /** 故障名称 */
  faultName?: string
  /** 故障时间 */
  faultTime?: string
  /** 主键ID */
  id?: number
  /** 故障相关图片（如现场照片等） */
  photos?: string
  /** 关联设备 */
  relatedDevice?: string
  /** 工单ID */
  workOrderId?: number
}

/** 工单处理检查项实体类 */
export interface WorkOrderHandleCheckItem {
  /** 处理后图片路径 */
  afterImage?: string
  /** 处理前图片路径 */
  beforeImage?: string
  /** 检查项 */
  checkItem?: string
  /** 创建时间 */
  createdAt?: string
  /** 示例图片路径 */
  exampleImage?: string
  /** 主键ID */
  id?: number
  /** 回填结果内容 */
  resultContent?: any
  /** 回填结果类型：text-文本，select-选择，image-图片 */
  resultType?: string
  /** 排序序号 */
  sort?: number
  /** 修改时间 */
  updatedAt?: string
  /** 工单ID */
  workOrderId?: number
}

export interface WorkOrderProcess {
  /** 创建时间 */
  createdAt?: string
  /** 创建人 */
  createdBy?: string
  /** 处理检查项JSON */
  handleCheckItemsJson?: string
  /** 主键ID */
  id?: number
  /** 进度描述 */
  processDesc?: string
  /** 进度名称 */
  processName?: string
  /** 工单ID */
  workOrderId?: number
}

export interface WorkOrder {
  /** 详细地址 */
  address?: string
  /** 审核方式：NONE-无需审核，SUB_CENTER-分中心审核，HEADQUARTER-总部二审 */
  auditMode?: string
  /** 运维商业务类型:1--户用,2--工商业,3--户+工 */
  businessType?: number
  /** 市ID */
  cityId?: number
  /** 市 */
  cityName?: string
  /** 工单关闭描述 */
  closeDesc?: string
  /** 工单关闭原因 */
  closeReason?: string
  /** 工单关闭时间 */
  closeTime?: string
  /** 工单关闭人 */
  closedBy?: string
  /** 工单配置检查项列表 */
  configCheckItems?: WorkOrderConfigCheckItem[]
  /** 工单配置ID */
  configId?: number
  /** 创建时间 */
  createdAt?: string
  /** 创建人 */
  createdBy?: string
  /** 截止时间 */
  deadline?: string
  /** 下发审核权限：SUB_CENTER-分中心，HEADQUARTER-总部 */
  dispatchAuditPermission?: string
  /** 下发方式：AUTO-自动，REVIEW-审核 */
  dispatchMode?: string
  /** 故障现象 */
  faultDescription?: string
  /** 工单故障信息 */
  faultInfo?: WorkOrderFaultInfo
  /** 故障等级 */
  faultLevel?: string
  /** 工单完成时间 */
  finishTime?: string
  /** 工单处理检查项列表 */
  handleCheckItems?: WorkOrderHandleCheckItem[]
  /** 处理时间 */
  handleTime?: string
  /** 处理人 */
  handler?: string
  /** 处理人ID */
  handlerId?: string
  /** 工单ID */
  id?: number
  /** 逆变器编号 */
  inverterSn?: string
  /** 是否质保期内 */
  isWarranty?: string
  /** 运维商编码 */
  opCode?: string
  /** 运维商会员id */
  opMemberId?: number
  /** 运维商名称 */
  opName?: string
  /** 服务商类别:建站服务商--STATION_SERVICE,三方运维服务商--THIRD_SERVICE */
  opType?: string
  /** 工单编号 */
  orderCode?: string
  /** 工单名称 */
  orderName?: string
  /** 工单来源 */
  orderSource?: string
  /** 工单状态 */
  orderStatus?: string
  /** 工单类型 */
  orderType?: string
  /** 是否超时 */
  overTime?: boolean
  /** 工单超期天数 */
  overTimeDays?: number
  /** 工单进度记录列表 */
  processes?: WorkOrderProcess[]
  /** 项目公司名称 */
  projectCompanyName?: string
  /** 省ID */
  provinceId?: number
  /** 省 */
  provinceName?: string
  /** 区ID */
  regionId?: number
  /** 区 */
  regionName?: string
  /** 备注 */
  remark?: string
  /** 备件申请编号 */
  sparePartApplyNo?: string
  /** 备件申请通过时间 */
  sparePartAuditPassTime?: string
  /** 备件审批状态:WAIT_AUDIT-待审核,AUDIT_OK-审核通过,AUDIT_REJECT-审核驳回 */
  sparePartAuditStatus?: string
  /** 关联资方 */
  specialFlag?: string
  /** 所属电站编码 */
  stationCode?: string
  /** 所属电站ID */
  stationId?: number
  /** 所属电站模式 */
  stationMode?: string
  /** 所属电站业主姓名 */
  stationName?: string
  /** 所属电站业主手机号 */
  stationPhone?: string
  /** 所属电站类型 */
  stationType?: string
  /** 街道ID */
  streetId?: number
  /** 街道 */
  streetName?: string
  /** 分中心代码 */
  subCenterCode?: string
  /** 分中心名称 */
  subCenterName?: string
  /** 提报人ID */
  submitterId?: string
  /** 超时时间(天) */
  timeoutDays?: number
  toAssign?: boolean
  toHeadAudit?: boolean
  toHeadDispatch?: boolean
  toProcess?: boolean
  toSubCenterAudit?: boolean
  toSubCenterDispatch?: boolean
  /** 修改时间 */
  updatedAt?: string
  /** 修改人 */
  updatedBy?: string
}

/**
 * 工单配置实体类 (derived from LightOperationWorkOrderConfig)
 * @description 工单配置实体类
 */
export interface WorkOrderConfig {
  /** @description 是否自动派单 */
  autoDispatch?: boolean
  /** @description 检查项列表 */
  checkItems?: WorkOrderConfigCheckItem[]
  /** @description 工单配置名称 */
  configName?: string
  /** @description 创建时间 */
  createdAt?: string
  /** @description 创建人 */
  createdBy?: string
  /** @description 是否由分中心派单 */
  dispatchBySubCenter?: boolean
  /** @description 下发方式：AUTO-自动，REVIEW-审核 */
  dispatchMode?: string
  /** @description 下发审核权限：SUB_CENTER-分中心，HEADQUARTER-总部 */
  dispatchReviewPermission?: string
  /** @description 故障ID */
  faultId?: number // int64
  /** @description 配置ID */
  id?: number // int64
  /** @description 工单类型 */
  orderType?: string
  /** @description 备注 */
  remark?: string
  /** @description 审核方式：NONE-无需审核，SUB_CENTER-分中心审核，HEADQUARTER-总部二审 */
  reviewMode?: string
  /** @description 方案ID */
  solutionId?: number // int64
  /** @description 状态：1-启用，0-禁用 */
  status?: number // int32
  /** @description 超时时间(天) */
  timeoutDays?: number // int32
  /** @description 修改时间 */
  updatedAt?: string
  /** @description 更新人 */
  updatedBy?: string
}

/**
 * 分页查询工单配置列表的参数
 */
export interface WorkorderConfigPageParams {
  /** 工单配置名称 */
  configName?: string
  /** 下发方式：AUTO-自动，REVIEW-审核 */
  dispatchMode?: string
  /** 故障ID */
  faultId?: number
  /** 工单类型 */
  orderType?: string
  /** 页码 */
  pageNum?: number
  /** 一页数据量 */
  pageSize?: number
  /** 方案ID */
  solutionId?: number
  /** 状态：1-启用，0-禁用 */
  status?: number
  /** 超时时间(天) */
  timeoutDays?: number
}

// Added based on api-temp.json definitions

/**
 * @description WorkOrderRejectReq
 */
export interface WorkOrderRejectReq {
  /**
   * 工单编号
   */
  orderCode?: string
  /**
   * 驳回描述
   */
  rejectDesc?: string
  /**
   * 驳回原因
   */
  rejectReason?: string
}

/**
 * 批量审批结果
 * @description BatchAuditResultDto
 */
export interface BatchAuditResultDto {
  /**
   * 是否全部成功
   */
  allSuccess?: boolean
  /**
   * 失败数量
   */
  failCount?: number
  /**
   * 失败的工单编号列表
   */
  failOrderCodes?: string[]
  /**
   * 成功数量
   */
  successCount?: number
  /**
   * 总工单数
   */
  total?: number
}

/**
 * 工单关闭请求参数
 * @description WorkOrderCloseReq
 */
export interface WorkOrderCloseReq {
  /**
   * 关闭描述
   */
  closeDesc?: string
  /**
   * 关闭原因
   */
  closeReason?: string
  /**
   * 工单编号
   */
  orderCode?: string
}

/**
 * 工单处理请求参数
 * @description WorkOrderHandleReq
 */
export interface WorkOrderHandleReq {
  /**
   * 处理检查项列表
   */
  handleCheckItems?: WorkOrderHandleCheckItem[]
  /**
   * 工单编号
   */
  orderCode?: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * 工单提报请求参数
 * @description WorkOrderSubmitReq
 */
export interface WorkOrderSubmitReq {
  /**
   * 工单配置id
   */
  configId?: number
  /**
   * 工单来源
   */
  orderSource?: string
  /**
   * 现场照片
   */
  photos?: string
  /**
   * 问题描述
   */
  problemDesc?: string
  /**
   * 电站编码
   */
  stationCode?: string
}

/**
 * @description Parameters for batch audit pass work orders.
 */
export type WorkorderBatchAuditPassParams = string[]

/**
 * @description Parameters for batch dispatch work orders.
 */
export type WorkorderBatchDispatchParams = string[]

/**
 * @description 工单效率统计数据传输对象
 */
export interface WorkOrderEfficiencyDto {
  /** 工单审核率（%） */
  auditRate?: number
  /** 已审核工单数 */
  auditedCount?: number
  /** 工单完成率（%） */
  finishRate?: number
  /** 已完成工单数 */
  finishedCount?: number
  /** 代理商/运维商名称 */
  opName?: string
  /** 需要审核的工单总数 */
  totalAuditCount?: number
  /** 工单总数 */
  totalCount?: number
}

/**
 * @description Parameters for GET /light/operation/workOrder/efficiency-op
 */
export interface WorkorderEfficiencyOpParams {
  /** 创建时间止 */
  createTimeEnd?: string
  /** 创建时间起 */
  createTimeStart?: string
  /** 运维商会员ID */
  opMemberId?: number
  /** 运维商名称 */
  opName?: string
  /** 工单类型 */
  orderType?: string
  /** 是否超时 */
  overTime?: boolean
  /** 页码 */
  pageNum?: number
  /** 一页数据量 */
  pageSize?: number
  /** 分中心编码 */
  subCenterCode?: string
}

/**
 * @description Parameters for GET /light/operation/workOrder/efficiency-total
 */
export interface WorkorderEfficiencyTotalParams {
  /** 创建时间止 */
  createTimeEnd?: string
  /** 创建时间起 */
  createTimeStart?: string
  /** 运维商会员ID */
  opMemberId?: number
  /** 运维商名称 */
  opName?: string
  /** 工单类型 */
  orderType?: string
  /** 是否超时 */
  overTime?: boolean
  /** 页码 */
  pageNum?: number
  /** 一页数据量 */
  pageSize?: number
  /** 分中心编码 */
  subCenterCode?: string
}
