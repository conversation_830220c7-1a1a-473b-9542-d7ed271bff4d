// src/types/api/Inspect.ts

/**
 * 巡检配置巡检项实体类
 */
export interface InspectionConfigCheckItem {
  /**
   * 巡检项
   */
  checkItem?: string;
  /**
   * 巡检配置ID
   */
  configId?: number;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 示例图片路径
   */
  exampleImage?: string;
  /**
   * 巡检项ID
   */
  id?: number;
  /**
   * 回填结果类型：text-文本，select-选择，img-图片
   */
  resultType?: string;
  /**
   * 排序序号
   */
  sort?: number;
  /**
   * 修改时间
   */
  updatedAt?: string;
}

/**
 * 巡检配置实体类
 */
export interface InspectionConfig {
  annual?: boolean;
  /**
   * 巡检项列表
   */
  checkItems?: InspectionConfigCheckItem[];
  /**
   * 巡检配置名称
   */
  configName?: string;
  /**
   * 巡检配置类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  configType?: string;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 创建人
   */
  createdBy?: string;
  /**
   * 配置ID
   */
  id?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 关联方案ID
   */
  solutionId?: number;
  /**
   * 电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
  /**
   * 状态：1-启用，0-禁用
   */
  status?: number;
  /**
   * 修改时间
   */
  updatedAt?: string;
  /**
   * 更新人
   */
  updatedBy?: string;
}

/**
 * 巡检计划实体类
 */
export interface InspectionPlan {
  /**
   * 巡检配置ID
   */
  configId?: number;
  /**
   * 巡检配置名称
   */
  configName?: string;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 创建人
   */
  createdBy?: string;
  /**
   * 计划结束日期
   */
  endDate?: string;
  /**
   * 计划ID
   */
  id?: number;
  /**
   * 巡检类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  inspectionType?: string;
  /**
   * 巡检计划名称
   */
  planName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 计划开始日期
   */
  startDate?: string;
  /**
   * 电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
  /**
   * 计划状态: CREATE-创建，DISPATCHED-已下发，COMPLETED-已完成
   */
  status?: string;
  /**
   * 关联分中心编码集合
   */
  subCenterCodeList?: string[];
  /**
   * 修改时间
   */
  updatedAt?: string;
  /**
   * 更新人
   */
  updatedBy?: string;
}

/**
 * 巡检工单检查项实体类
 */
export interface InspectionWorkOrderCheckItem {
  /**
   * 处理后图片路径
   */
  afterImage?: string;
  /**
   * 处理前图片路径
   */
  beforeImage?: string;
  /**
   * 检查项
   */
  checkItem?: string;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 示例图片路径
   */
  exampleImage?: string;
  /**
   * 检查项ID
   */
  id?: number;
  /**
   * 回填结果内容
   */
  resultContent?: any;
  /**
   * 回填结果类型：text-文本，select-选择，img-图片
   */
  resultType?: string;
  /**
   * 排序序号
   */
  sort?: number;
  /**
   * 修改时间
   */
  updatedAt?: string;
  /**
   * 巡检工单ID
   */
  workOrderId?: number;
}

/**
 * 巡检工单实体类
 */
export interface InspectionWorkOrder {
  /**
   * 详细地址
   */
  address?: string;
  /**
   * 运维商业务类型:1--户用,2--工商业,3--户+工
   */
  businessType?: number;
  /**
   * 市ID
   */
  cityId?: number;
  /**
   * 市
   */
  cityName?: string;
  /**
   * 工单配置检查项列表
   */
  configItems?: InspectionConfigCheckItem[];
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 任务截止日期
   */
  endDate?: string;
  /**
   * 工单处理检查项列表
   */
  handleItems?: InspectionWorkOrderCheckItem[];
  /**
   * 处理时间
   */
  handleTime?: string;
  /**
   * 处理人
   */
  handler?: string;
  /**
   * 处理人ID
   */
  handlerId?: string;
  /**
   * 巡检工单ID
   */
  id?: number;
  /**
   * 巡检类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  inspectionType?: string;
  /**
   * 运维商会员id
   */
  opMemberId?: number;
  /**
   * 运维商名称
   */
  opName?: string;
  /**
   * 工单编号
   */
  orderCode?: string;
  /**
   * 工单名称
   */
  orderName?: string;
  /**
   * 工单状态
   */
  orderStatus?: string;
  /**
   * 工单类型
   */
  orderType?: string;
  /**
   * 是否超时
   */
  overTime?: boolean;
  /**
   * 巡检计划ID
   */
  planId?: number;
  /**
   * 巡检计划名称
   */
  planName?: string;
  /**
   * 省ID
   */
  provinceId?: number;
  /**
   * 省
   */
  provinceName?: string;
  /**
   * 区ID
   */
  regionId?: number;
  /**
   * 区
   */
  regionName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 关联资方
   */
  specialFlag?: string;
  /**
   * 任务开始日期
   */
  startDate?: string;
  /**
   * 所属电站编码
   */
  stationCode?: string;
  /**
   * 所属电站ID
   */
  stationId?: number;
  /**
   * 所属电站模式
   */
  stationMode?: string;
  /**
   * 所属电站业主姓名
   */
  stationName?: string;
  /**
   * 所属电站业主手机号
   */
  stationPhone?: string;
  /**
   * 巡检电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
  /**
   * 街道ID
   */
  streetId?: number;
  /**
   * 街道
   */
  streetName?: string;
  /**
   * 分中心代码
   */
  subCenterCode?: string;
  /**
   * 分中心名称
   */
  subCenterName?: string;
}

export type InspectPlanCreateParams = InspectionPlan;

export interface InspectPlanDeleteParams {
  /**
   * id
   */
  id: number;
}

export interface InspectPlanGetParams {
  /**
   * id
   */
  id: number;
}

export interface InspectPlanListParams {
  /**
   * 巡检配置ID
   */
  configId?: number;
  /**
   * 巡检类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  inspectionType?: string;
  /**
   * 页码
   */
  pageNum?: number;
  /**
   * 一页数据量
   */
  pageSize?: number;
  /**
   * 电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
  /**
   * 计划状态: CREATE-创建，DISPATCHED-已下发，COMPLETED-已完成
   */
  status?: string;
}

export interface InspectPlanPageParams {
  /**
   * 巡检配置ID
   */
  configId?: number;
  /**
   * 巡检类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  inspectionType?: string;
  /**
   * 页码
   */
  pageNum?: number;
  /**
   * 一页数据量
   */
  pageSize?: number;
  /**
   * 电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
  /**
   * 计划状态: CREATE-创建，DISPATCHED-已下发，COMPLETED-已完成
   */
  status?: string;
}

export type InspectPlanUpdateParams = InspectionPlan;


/**
 * @description 巡检计划统计结果
 */
export interface InspectionPlanStatistics {
  /**
   * @description 市
   */
  cityName?: string;
  /**
   * @description 已巡检数量
   */
  inspectedCount?: number;
  /**
   * @description 运维商ID
   */
  opMemberId?: number;
  /**
   * @description 运维商名称
   */
  opName?: string;
  /**
   * @description 计划ID
   */
  planId?: number;
  /**
   * @description 计划名称
   */
  planName?: string;
  /**
   * @description 巡检进度（整数百分比）
   */
  progress?: number;
  /**
   * @description 省
   */
  provinceName?: string;
  /**
   * @description 区
   */
  regionName?: string;
  /**
   * @description 关联资方
   */
  specialFlag?: string;
  /**
   * @description 电站数量
   */
  stationCount?: number;
  /**
   * @description 开始时间
   */
  startDate?: string;
  /**
   * @description 结束时间
   */
  endDate?: string;
}

/**
 * @description InspectionPlanStatisticsTotal
 */
export interface InspectionPlanStatisticsTotal {
  /**
   * @description 已巡检数量
   */
  inspectedCount?: number;
  /**
   * @description 巡检进度（整数百分比）
   */
  progress?: number;
  /**
   * @description 电站数量
   */
  stationCount?: number;
}

export interface InspectWorkOrderGetParams {
  /**
   * @description orderCode
   */
  orderCode: string;
}

export interface InspectWorkOrderListParams {
  /**
   * @description 处理人ID
   */
  handlerId?: string;
  /**
   * @description 巡检类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  inspectionType?: string;
  /**
   * @description 所属运维商id
   */
  opMemberId?: number;
  /**
   * @description 所属运维商名称
   */
  opName?: string;
  /**
   * @description 工单编号
   */
  orderCode?: string;
  /**
   * @description 工单名称
   */
  orderName?: string;
  /**
   * @description 工单状态
   */
  orderStatus?: string;
  /**
   * @description 是否超时
   */
  overTime?: boolean;
  /**
   * @description 页码
   */
  pageNum?: number;
  /**
   * @description 一页数据量
   */
  pageSize?: number;
  /**
   * @description 巡检计划ID
   */
  planId?: number;
  /**
   * @description 关联资方
   */
  specialFlag?: string;
  /**
   * @description 任务开始时间-起始
   */
  startDateBegin?: string;
  /**
   * @description 任务开始时间-结束
   */
  startDateEnd?: string;
  /**
   * @description 所属电站编码
   */
  stationCode?: string;
  /**
   * @description 所属电站ID
   */
  stationId?: number;
  /**
   * @description 所属电站业主姓名
   */
  stationName?: string;
  /**
   * @description 电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
}

export interface InspectWorkOrderPageParams {
  /**
   * @description 处理人ID
   */
  handlerId?: string;
  /**
   * @description 巡检类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  inspectionType?: string;
  /**
   * @description 所属运维商id
   */
  opMemberId?: number;
  /**
   * @description 所属运维商名称
   */
  opName?: string;
  /**
   * @description 工单编号
   */
  orderCode?: string;
  /**
   * @description 工单名称
   */
  orderName?: string;
  /**
   * @description 工单状态
   */
  orderStatus?: string;
  /**
   * @description 是否超时
   */
  overTime?: boolean;
  /**
   * @description 页码
   */
  pageNum?: number;
  /**
   * @description 一页数据量
   */
  pageSize?: number;
  /**
   * @description 巡检计划ID
   */
  planId?: number;
  /**
   * @description 关联资方
   */
  specialFlag?: string;
  /**
   * @description 任务开始时间-起始
   */
  startDateBegin?: string;
  /**
   * @description 任务开始时间-结束
   */
  startDateEnd?: string;
  /**
   * @description 所属电站编码
   */
  stationCode?: string;
  /**
   * @description 所属电站ID
   */
  stationId?: number;
  /**
   * @description 所属电站业主姓名
   */
  stationName?: string;
  /**
   * @description 电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
}

export interface InspectWorkOrderStatisticsParams {
  /**
   * @description 市ID
   */
  cityId?: number;
  /**
   * @description 巡检类型
   */
  inspectionType?: string;
  /**
   * @description 运维商ID
   */
  opMemberId?: number;
  /**
   * @description 运维商名称
   */
  opName?: string;
  /**
   * @description 页码
   */
  pageNum?: number;
  /**
   * @description 一页数据量
   */
  pageSize?: number;
  /**
   * @description 巡检计划ID
   */
  planId?: number;
  /**
   * @description 进度百分比最大值
   */
  progressMax?: number;
  /**
   * @description 进度百分比最小值
   */
  progressMin?: number;
  /**
   * @description 省ID
   */
  provinceId?: number;
  /**
   * @description 区ID
   */
  regionId?: number;
  /**
   * @description 关联资方
   */
  specialFlag?: string;
  /**
   * @description 巡检电站类型
   */
  stationType?: string;
  /**
   * @description 分中心编码列表
   */
  subCenterList?: string[];
}

export interface InspectWorkOrderStatisticsTotalParams {
  /**
   * @description 市ID
   */
  cityId?: number;
  /**
   * @description 巡检类型
   */
  inspectionType?: string;
  /**
   * @description 运维商ID
   */
  opMemberId?: number;
  /**
   * @description 运维商名称
   */
  opName?: string;
  /**
   * @description 页码
   */
  pageNum?: number;
  /**
   * @description 一页数据量
   */
  pageSize?: number;
  /**
   * @description 巡检计划ID
   */
  planId?: number;
  /**
   * @description 进度百分比最大值
   */
  progressMax?: number;
  /**
   * @description 进度百分比最小值
   */
  progressMin?: number;
  /**
   * @description 省ID
   */
  provinceId?: number;
  /**
   * @description 区ID
   */
  regionId?: number;
  /**
   * @description 关联资方
   */
  specialFlag?: string;
  /**
   * @description 巡检电站类型
   */
  stationType?: string;
  /**
   * @description 分中心编码列表
   */
  subCenterList?: string[];
}

/**
 * Parameters for handling an inspection work order.
 * Path: /light/operation/inspection/work-order/handle
 */
export interface InspectHandleParams {
  /**
   * @description 处理巡检项列表
   */
  handleCheckItems?: InspectionWorkOrderCheckItem[]
  /**
   * @description 工单编号
   */
  orderCode?: string
  /**
   * @description 备注
   */
  remark?: string
}

/**
 * Parameters for listing inspection work orders.
 * Path: /light/operation/inspection/work-order/list
 */
export interface InspectListParams {
  /**
   * @description 处理人ID
   */
  handlerId?: string;
  /**
   * @description 巡检类型: ANNUAL-年度巡检，TEMPORARY-临时巡检
   */
  inspectionType?: string;
  /**
   * @description 所属运维商id
   */
  opMemberId?: number;
  /**
   * @description 所属运维商名称
   */
  opName?: string;
  /**
   * @description 工单编号
   */
  orderCode?: string;
  /**
   * @description 工单名称
   */
  orderName?: string;
  /**
   * @description 工单状态
   */
  orderStatus?: string;
  /**
   * @description 是否超时
   */
  overTime?: boolean;
  /**
   * @description 页码
   */
  pageNum?: number;
  /**
   * @description 一页数据量
   */
  pageSize?: number;
  /**
   * @description 巡检计划ID
   */
  planId?: number;
  /**
   * @description 关联资方
   */
  specialFlag?: string;
  /**
   * @description 任务开始时间-起始
   */
  startDateBegin?: string;
  /**
   * @description 任务开始时间-结束
   */
  startDateEnd?: string;
  /**
   * @description 所属电站编码
   */
  stationCode?: string;
  /**
   * @description 所属电站ID
   */
  stationId?: number;
  /**
   * @description 所属电站业主姓名
   */
  stationName?: string;
  /**
   * @description 电站类型: COMMON-户用，CM-工商业，PUB_BUILD-公共租赁
   */
  stationType?: string;
}
