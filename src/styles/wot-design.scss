/*#ifndef APP-NVUE*/
:root,
page {
  --wot-color-theme: #37ACFE;
  --wot-color-gray-2: #f2f3f5;

  --wot-input-placeholder-color: #bfbfbf;

  --wot-message-box-width: 80vw;
  --wot-message-box-radius: 12px;
  --wot-message-box-padding: 0px;
  --wot-message-box-bg: #FFFFFF;
  --wot-message-box-title-fs: 18px;
  --wot-message-box-title-color: 2C3E50;
  --wot-message-box-content-fs: 15px;
  --wot-message-box-content-color: #606266;

  --wot-progress-height: 6px;

  image {
    border-radius: 6px;
  }
}

.wd-calendar__confirm {
  .wd-button {
    border-radius: var(--wot-button-large-radius, 8px) !important;
  }
}

.wd-select-picker__footer {
  .wd-button {
    border-radius: var(--wot-button-large-radius, 8px) !important;
  }
}


.wd-message-box {
  background-color: var(--wot-message-box-bg);
  border-radius: var(--wot-message-box-radius);
  width: var(--wot-message-box-width);
  padding: 0;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  .wd-message-box__title {
    padding: 16px;
    color: var(--wot-message-box-title-color);
    font-size: var(--wot-message-box-title-fs);
    font-weight: 500;
    line-height: 1.2;
    text-align: left;
    border-bottom: 1px solid #F2F2F2;
    box-sizing: border-box;
  }

  .wd-message-box__main,
  .wd-message-box__content {
    padding: 36px 24px;
    color: var(--wot-message-box-content-color);
    font-size: var(--wot-message-box-content-fs);
    font-weight: 500;
    text-align: center;
    box-sizing: border-box;
  }

  .wd-message-box__actions {
    display: flex;
    height: 60px;
    border-top: 1px solid #F9FAFB;
    padding: 0;

    .wd-message-box__actions-btn {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      ;
      margin: 0px;
      border-radius: 0px;

      &:first-child {
        background-color: white;
      }
    }
  }
}

/*#endif*/