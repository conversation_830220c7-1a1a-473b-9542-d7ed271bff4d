<script setup lang="ts">
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useQuery } from '@tanstack/vue-query';
import { useToast } from 'wot-design-uni';
import { getSolution } from '@/api/solution';
import type { Solution } from '@/types/api/Solution';
import { openDocument, navigateTo } from '@uni-helper/uni-promises';
import { useDictStore } from '@/store';

interface DocumentItem {
  name: string;
  type: 'pdf' | 'video';
  icon: string;
  viewIcon: string;
  url?: string;
}

const solutionId = ref<number | null>(null);
const toast = useToast();
const dictStore = useDictStore()

onLoad((options) => {
  if (options?.id) {
    solutionId.value = parseInt(options.id, 10);
  } else {
    toast.error({ msg: '方案ID缺失' });
    uni.navigateBack();
  }
  dictStore.fetchDict(['solution_type'])
});

const {
  data: solutionData,
  isLoading,
  isError,
  error,
} = useQuery<Solution, Error>({
  queryKey: ['solutionDetail', solutionId],
  queryFn: async () => {
    if (solutionId.value === null) {
      throw new Error('Solution ID is missing');
    }
    return getSolution({ id: solutionId.value });
  },
  enabled: computed(() => solutionId.value !== null),
});


const documentList = computed<DocumentItem[]>(() => {
  const items: DocumentItem[] = [];
  const data = solutionData.value;
  if (!data) return [];

  const dslDocDefinitions = [
    { name: `${data.name}.pdf`, type: 'pdf', icon: 'i-carbon-document-pdf', apiKey: 'file' },
    { name: `${data.name}.mp4`, type: 'video', icon: 'i-carbon-video', apiKey: 'video' },
  ];

  dslDocDefinitions.forEach(def => {
    let urlToUse: string | undefined = undefined;

    if (def.apiKey === 'file' && data.file) {
      urlToUse = data.file;
    } else if (def.apiKey === 'video' && data.video) {
      urlToUse = data.video;
    }

    if (urlToUse) {
      items.push({
        name: def.name,
        type: def.type as 'pdf' | 'video',
        icon: def.icon,
        viewIcon: 'i-carbon-view',
        url: urlToUse,
      });
    }
  });
  return items;
});

async function handleDocumentClick(doc: DocumentItem) {
  if (doc.type === 'pdf') {
    toast.loading({ msg: '正在准备文件...' });
    try {
      const downloadResult = await uni.downloadFile({ url: doc.url! });
      if (downloadResult.statusCode === 200) {
        await openDocument({ filePath: downloadResult.tempFilePath });
      } else {
        toast.error({ msg: `文件下载失败: ${downloadResult.statusCode}` });
      }
    } catch (e: any) {
      toast.error({ msg: `打开文件失败: ${(e as Error).message || '未知错误'}` });
    } finally {
      toast.close()
    }
  } else if (doc.type === 'video') {
    navigateTo({ url: `/pages/common/video-player?url=${encodeURIComponent(doc.url!)}` });
  }
}
</script>

<template>
  <view class="solution-detail-page">
    <template v-if="isLoading">
      <view class="status-message">加载中...</view>
    </template>
    <template v-else-if="isError">
      <view class="status-message status-message--error">加载失败: {{ error?.message }}</view>
    </template>
    <template v-else-if="solutionData">
      <view class="solution-cards-container">
        <view class="solution-card">
          <view class="solution-card__name">
            {{ solutionData.name || '未知方案' }}
          </view>
          <view class="solution-card__footer">
            <view class="solution-card__time">
              创建时间：{{ solutionData?.createdAt }}
            </view>
          </view>
        </view>

        <view class="solution-card">
          <view class="solution-card__title">
            方案信息
          </view>
          <view class="solution-card__info-rows">
            <view class="solution-card__info-row">
              <text class="solution-card__info-label">所属分类</text>
              <text class="solution-card__info-value">{{ solutionData.categoryName || '-' }}</text>
            </view>
            <view class="solution-card__info-row">
              <text class="solution-card__info-label">方案类型</text>
              <text class="solution-card__info-value">{{ dictStore.getDictLabel('solution_type', solutionData.solutionType)}}</text>
            </view>
            <view class="solution-card__info-row">
              <text class="solution-card__info-label">方案描述</text>
              <text class="solution-card__info-value">{{ solutionData.description || '-' }}</text>
            </view>
          </view>
        </view>

        <view class="solution-card">
          <view class="solution-card__title">
            方案文档
          </view>
          <view class="solution-card__documents-container">
            <template v-if="documentList.length > 0">
              <view
                v-for="(doc, index) in documentList"
                :key="index"
                class="document-item"
                @click="handleDocumentClick(doc)"
              >
                <view class="document-item__content">
                  <view :class="doc.icon" class="document-item__icon" />
                  <text class="document-item__name">{{ doc.name }}</text>
                </view>
                <view :class="doc.viewIcon" class="document-item__view-icon" />
              </view>
            </template>
            <template v-else>
              <view class="status-message status-message--empty">暂无相关文档</view>
            </template>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "方案详情",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.solution-detail-page {
  background-color: #f7f7f5;
  min-height: 100vh;
  padding-bottom: 20px;
}

.status-message {
  padding: 16px;
  text-align: center;
  color: #6b7280;

  &--error {
    color: #ef4444;
  }

  &--empty {
    text-align: center;
    color: #9ca3af;
    padding-top: 16px;
    padding-bottom: 16px;
  }
}

.solution-cards-container {
  padding: 14px;
  & > * + * {
    margin-top: 15px;
  }
}

.solution-card {
  background-color: #fff;
  padding: 14px;
  border-radius: 7px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);

  &__name {
    font-size: 17.5px;
    color: #000000;
    margin-bottom: 10px;
  }

  &__meta-info {
    font-size: 12.25px;
    color: #6b7280;
    margin-bottom: 7px;
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__time {
    font-size: 12.25px;
    color: #6b7280;
  }

  &__title {
    font-size: 15.75px;
    color: #000000;
    margin-bottom: 15px;
  }

  &__info-rows {
    & > * + * {
      margin-top: 10px;
    }
  }

  &__info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }

  &__info-label {
    color: #4b5563;
  }

  &__info-value {
    color: #111827;
  }

  &__documents-container {
    & > * + * {
      margin-top: 10px;
    }
  }
}

.document-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f9fafb;
  padding: 10.5px;
  border-radius: 7px;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }

  &__content {
    display: flex;
    align-items: center;
  }

  &__icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    color: #3b82f6;
  }

  &__name {
    font-size: 14px;
    color: #374151;
  }

  &__view-icon {
    width: 20px;
    height: 20px;
    color: #9ca3af;
  }
}
</style>
