<script lang="ts" setup>
import { useUserStore } from '@/store'
import { navigateTo } from '@uni-helper/uni-promises'
import { ref } from 'vue'

const userName = ref('')
const weatherTemp = ref('25')
const weatherDesc = ref('晴转多云')
const unreadMessages = ref(4)
const searchQuery = ref('')
const userStore = useUserStore()

userName.value = userStore.userInfo?.name || ''

const todoItems = ref([
  {
    type: '待下发',
    count: 12,
    details: ['等待执行 类型1', '等待执行 类型1'],
    color: 'todo-card--green',
    textColor: 'todo-card__type--green',
    icon: '/static/home/<USER>',
    badgeBg: 'todo-card__badge-bg--green',
  },
  {
    type: '待审核',
    count: 12,
    details: ['李工·并网申请签批', '倒计时4h'],
    color: 'todo-card--blue',
    textColor: 'todo-card__type--blue',
    icon: '/static/home/<USER>',
    badgeBg: 'todo-card__badge-bg--blue',
  },
  // {
  //   type: '已超时',
  //   count: 12,
  //   details: ['组串失配率超阈值15%', '影响日发电量8%'],
  //   color: 'todo-card--yellow',
  //   textColor: 'todo-card__type--yellow',
  //   icon: 'i-carbon-warning-alt',
  //   badgeBg: 'todo-card__badge-bg--yellow',
  // },
])

const menuItems = ref([
  {
    name: '电站中心',
    icon: 'i-carbon-building',
    path: '/static/home/<USER>',
    url: '/pages/station/index',
  },
  {
    name: '方案库',
    icon: 'i-carbon-document-multiple-01',
    path: '/static/home/<USER>',
    url: '/pages/solution/list',
  },
  {
    name: '租金查询',
    icon: 'i-carbon-money',
    path: '/static/home/<USER>',
    url: '/pages/rent/index',
  },
  {
    name: '智能客服',
    icon: 'i-carbon-headset',
    path: '/static/home/<USER>',
    url: '/pages/service/index',
  },
  {
    name: '培训中心',
    icon: 'i-carbon-education',
    path: '/static/home/<USER>',
    url: '/pages/training-center/index',
  },
  {
    name: '消息管理',
    icon: 'i-carbon-chat',
    path: '/static/home/<USER>',
    url: '/pages/message/list',
  },
])

function handleSearch() {
  navigateTo({
    url: `/pages/monitor/monitor`,
  })
}

function handleContactUs() {
  uni.showToast({ title: '点击了联系我们', icon: 'none' })
}

function handleTodoLink() {
  uni.showToast({ title: '点击了全部待办', icon: 'none' })
}

function handleNotificationClick() {
  uni.showToast({ title: '点击了通知中心', icon: 'none' })
}

const goToStation = () => {
  navigateTo({
    url: `/pages/station/station`,
  })
}

const goToPage = (url: string) => {
  navigateTo({
    url,
  })
}
</script>

<template>
  <view class="index-page min-h-screen pb-16">
    <view class="header relative overflow-hidden">
      <view class="header__content flex justify-between items-center mb-4">
        <view class="header__user-info flex items-center">
          <image src="/static/logo.png" class="header__avatar" />
          <text class="header__greeting">欢迎回来{{ `${userName && `,${userName}`}` }}</text>
        </view>
        <view class="header__weather text-right">
          <view class="header__temperature">{{ weatherTemp }}°C</view>
          <view class="header__weather-desc flex items-center justify-end">
            {{ weatherDesc }}
          </view>
        </view>
      </view>

      <view class="search-bar flex items-center" @click="goToStation">
        <view class="search-bar__icon i-carbon-search mr-2" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="请输入电站名称或电站编号"
          class="search-bar__input flex-1"
          placeholder-class="search-bar__placeholder"
          @confirm="handleSearch"
        />
        <button class="search-bar__button ml-2" @click="handleSearch">搜索</button>
      </view>
    </view>

    <view class="banner">
      <image src="/static/home/<USER>" />
    </view>

    <view class="menu-grid py-4">
      <view class="grid grid-cols-4 gap-4 text-center">
        <view
          v-for="item in menuItems"
          :key="item.name"
          class="menu-grid__item flex flex-col items-center"
          @click="goToPage(item.url)"
        >
          <view class="menu-grid__icon-wrapper flex items-center justify-center mb-1">
            <image :src="item.path" class="menu-grid__icon-img" mode="aspectFit" />
          </view>
          <text class="menu-grid__text">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <view class="notification-bar mb-4" @click="handleNotificationClick">
      <view class="notification-bar__content flex items-center">
        <image class="notification-bar__icon mr-2" src="/static/home/<USER>" />
        <text class="notification-bar__prefix mr-1">通知中心 |</text>
        <text class="notification-bar__text flex-1 truncate"
          >您有{{ unreadMessages }}条未读消息</text
        >
        <view class="notification-bar__suffix i-carbon-chevron-right ml-1" />
      </view>
    </view>

    <view class="todo-section mb-4 p-4">
      <view class="todo-section__header flex justify-between items-center mb-2">
        <text class="todo-section__title">我的待办</text>
        <view class="todo-section__all-link flex items-center" @click="handleTodoLink">
          <text>全部</text>
          <view class="i-carbon-chevron-right ml-0.5" />
        </view>
      </view>
      <view class="todo-section__grid grid grid-cols-2 gap-3">
        <view
          v-for="(item, index) in todoItems"
          :key="index"
          class="todo-card flex justify-between items-start"
          :class="[item.color]"
        >
          <view class="todo-card__content min-w-0 flex-1">
            <view class="todo-card__header flex items-center mb-1">
              <image :src="item.icon" class="w-6 h-6 mr-1" />
              <text class="todo-card__type" :class="item.textColor">{{ item.type }}</text>
            </view>
            <view class="todo-card__details">
              <view v-for="(detail, i) in item.details" :key="i" class="todo-card__detail-item">{{
                detail
              }}</view>
            </view>
          </view>
          <view class="todo-card__badge flex items-center justify-center" :class="item.badgeBg">
            {{ item.count }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  }
}
</route>

<style scoped lang="scss">
.index-page {
  padding: 0px 16px;
  padding-bottom: 60px;
  background-color: #f3f4f6;
  background-image: url('/static/home/<USER>');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.header {
  padding: 40px 0px 16px;
  color: white;

  .header__avatar {
    width: 40px;
    height: 40px;
    border-radius: 9999px;
    margin-right: 8px;
    border: 2px solid white;
  }

  .header__greeting {
    font-size: 18px;
    line-height: 28px;
    font-weight: 500;
  }

  .header__temperature {
    font-size: 24px;
    line-height: 32px;
    font-weight: 600;
  }

  .header__weather-desc {
    font-size: 12px;
    line-height: 16px;
  }
}

.search-bar {
  background-color: white;
  border-radius: 9999px;
  padding: 2px 16px;
  padding-right: 4px;
  color: #6b7280;
  height: 36px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);

  .search-bar__icon {
    font-size: 20px;
    line-height: 28px;
  }

  .search-bar__input {
    font-size: 14px;
    line-height: 20px;
    background-color: transparent;
    outline: none;
  }

  .search-bar__input:focus {
    outline: none;
  }

  .search-bar__placeholder {
    color: #9ca3af;
    font-size: 14px;
  }

  .search-bar__button {
    background-color: $uni-color-primary;
    color: white;
    font-size: 14px;
    line-height: 24px;
    padding: 4px 16px;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
  }

  .search-bar__button:active {
    opacity: 0.8;
  }
}

.banner {
  display: flex;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);

  > image {
    height: 120px;
    width: 100%;
  }
}

.menu-grid__icon-wrapper {
  width: 48px;
  height: 48px;
  // background-color: white;
  // border-radius: 0.5rem;
  // box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.menu-grid__icon-img {
  width: 42px;
  height: 42px;
}

.menu-grid__text {
  font-size: 12px;
  line-height: 16px;
  color: #4b5563;
}

.notification-bar__content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  padding: 12px;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
}

.notification-bar__icon {
  width: 20px;
  height: 20px;
}

.notification-bar__prefix {
  color: #2563eb;
  font-weight: 500;
}

.notification-bar__text {
  color: #374151;
}

.notification-bar__suffix {
  color: #9ca3af;
}

.todo-section {
  border-radius: 10px;
  background-color: white;
}

.todo-section__title {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: #1f2937;
}

.todo-section__all-link {
  font-size: 14px;
  line-height: 20px;
  color: #6b7280;
  cursor: pointer;
}

.todo-card {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  padding: 12px;
  flex-direction: column;
  min-height: 140px;
}

.todo-card--green {
  background-color: #d1fae5;
}

.todo-card--blue {
  background-color: #dbeafe;
}

.todo-card--yellow {
  background-color: #fef3c7;
}

.todo-card__icon {
  font-size: 20px;
  line-height: 28px;
}

.todo-card__type {
  font-weight: 500;
}

.todo-card__type--green {
  color: #059669;
}

.todo-card__type--blue {
  color: #2563eb;
}

.todo-card__type--yellow {
  color: #d97706;
}

.todo-card__detail-item {
  font-size: 12px;
  line-height: 16px;
  color: #6b7280;
}

.todo-card__badge {
  width: 40px;
  height: 40px;
  border-radius: 9999px;
  font-size: 18px;
  line-height: 28px;
  font-weight: 700;
  align-self: flex-end;
}

.todo-card__badge-bg--green {
  background-color: #a7f3d0;
  color: #059669;
}

.todo-card__badge-bg--blue {
  background-color: #bfdbfe;
  color: #2563eb;
}

.todo-card__badge-bg--yellow {
  background-color: #fde68a;
  color: #d97706;
}
</style>
