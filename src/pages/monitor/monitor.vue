<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { getStationPage } from '@/api/station'
import type { Station, StationPageParams } from '@/types/api/Station'
import StationCard from '@/pages/station/components/StationCard.vue'
import { navigateTo } from '@uni-helper/uni-promises'

const keyword = ref('')

const paging = ref<ZPagingRef>()
const stationList = ref<Station[]>([])
const pagnation = reactive({
  pageSize: 20,
  total: 0,
})

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: StationPageParams = {
    pageNum,
    pageSize,
  }
  if (keyword.value) {
    if (/^\d{11}$/.test(keyword.value)) {
      params.phone = keyword.value
    } else if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    } else {
      params.name = keyword.value
    }
  }

  try {
    const res = await getStationPage(params)
    paging.value?.completeByTotal(res.content, res.totalElements)
    pagnation.total = res.totalElements
  } catch (error) {
    paging.value?.complete(false)
  }
}

const goToDetail = (item: Station) => {
  navigateTo({
    url: `/pages/station/detail?stationCode=220323095607765`, // ${item.stationCode}`,
  })
}
</script>

<template>
  <view class="monitor-page">
    <search-bar
      v-model="keyword"
      @search="onSearch"
      placeholder="请输入电站编号/电站名称/手机号码"
    />

    <view class="px-3 text-sm text-gray">
      <text>结果：</text>
      <text class="text-primary">{{ pagnation.total }}</text>
      <text>户</text>
    </view>

    <!-- 电站列表 -->
    <z-paging
      ref="paging"
      v-model="stationList"
      class="station-list-paging"
      :fixed="false"
      @query="queryList"
      :default-page-size="pagnation.pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
    >
      <view class="station-list-content">
        <station-card
          v-for="item in stationList"
          :key="item.id || item.stationCode"
          :item="item"
          @click="goToDetail"
        />
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "监控中心"
  }
}
</route>

<style scoped lang="scss">
.monitor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .station-list-paging {
    flex: 1;
  }

  .station-list-content {
    padding: 8px 12px;
  }
}
</style>
