<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { navigateTo } from '@uni-helper/uni-promises'
import { getMessagePage, markMessageRead, batchMarkMessageRead } from '@/api/message'
import type { Message, MessagePageParams } from '@/types/api/Message'
import dayjs from 'dayjs'

const paging = ref<ZPagingInstance | null>(null)
const displayedMessages = ref<(Message & { unread: boolean })[]>([])

const currentTab = ref('SYSTEM')
const tabs = [
  { title: '业务消息', value: 'BUSINESS', messageType: 'BUSINESS' },
  { title: '系统消息', value: 'SYSTEM', messageType: 'SYSTEM' },
]

const queryParams = reactive<MessagePageParams>({
  pageNum: 1,
  pageSize: 10,
  messageType: 'SYSTEM',
})

const fetchDataList = async (pageNo: number, pageSize: number) => {
  queryParams.pageNum = pageNo
  queryParams.pageSize = pageSize
  try {
    const response = await getMessagePage(queryParams)
    const newItems = response.content.map(apiMsg => {
      const existingItem = displayedMessages.value.find(dm => dm.id === apiMsg.id)
      return {
        ...apiMsg,
        unread: existingItem ? existingItem.unread : apiMsg.readStatus === 'UNREAD',
      }
    })
    paging.value?.completeByTotal(newItems, response.totalElements)
  } catch (error) {
    console.error('Failed to fetch message list:', error)
    paging.value?.complete(false)
  }
}

const onTabChange = (event: { name: string } ) => {
  queryParams.messageType = event.name
  paging.value?.reload()
}

const handleViewDetail = async (messageId: string) => {
  navigateTo({ url: `/pages/message/detail?id=${messageId}` })
  const message = displayedMessages.value.find(m => String(m.id) === messageId)
  if (message && message.unread) {
    message.unread = false
    await markMessageRead(Number(messageId))
  }
}

const markAsRead = async (messageId: string) => {
  const message = displayedMessages.value.find(m => String(m.id) === messageId)
  if (message && message.unread) {
    message.unread = false
    await markMessageRead(Number(messageId))
  }
}

const markAllAsRead = async () => {
  const currentTypeMessagesToUpdate = displayedMessages.value.filter(
    dm => dm.messageType === queryParams.messageType && dm.unread,
  )

  if (currentTypeMessagesToUpdate.length === 0) return

  const messageIdsToMarkRead = currentTypeMessagesToUpdate.map(dm => dm.id)

  await batchMarkMessageRead({ messageIds: messageIdsToMarkRead })
  currentTypeMessagesToUpdate.forEach(message => {
    message.unread = false
  })
}

const reloadList = () => {
  paging.value?.reload()
}
</script>

<template>
  <view class="message-list-page">
    <wd-tabs v-model="currentTab" custom-class="message-tabs" @change="onTabChange">
      <template v-for="item in tabs" :key="item">
        <wd-tab :title="item.title" :name="item.value" />
      </template>
    </wd-tabs>
    <view class="read-all-section">
      <text class="read-all-button" @click="markAllAsRead">一键已读</text>
    </view>

    <z-paging
      ref="paging"
      v-model="displayedMessages"
      class="list-paging-component"
      :fixed="false"
      auto-height
      @query="fetchDataList"
      :default-page-size="queryParams.pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
    >
      <view class="message-items-container">
        <wd-swipe-action
          v-for="item in displayedMessages"
          :key="item.id"
          :auto-close="true"
        >
          <view class="message-item" @click="handleViewDetail(String(item.id))">
            <view class="message-item__status">
              <view v-if="item.unread" class="unread-dot" />
              <view v-else class="read-dot" />
            </view>
            <view class="message-item__content">
              <view class="message-item__header">
                <text class="message-item__title">{{ item.subject }}</text>
                <text class="message-item__time">{{ dayjs(item.createdAt).format('YYYY-MM-DD HH:mm') }}</text>
              </view>
              <text class="message-item__body">{{ item.createdByName }}</text>
            </view>
            <view class="message-item__action">
              <text class="view-detail-text">查看 ></text>
            </view>
          </view>
          <template #right>
            <view style="display: flex; height: 100%;">
              <view
                v-if="item.unread"
                style="background-color: #67C23A; color: #FFFFFF; font-size: 14px; display: flex; align-items: center; justify-content: center; flex: 1; height: 100%; padding: 0 15px; box-sizing: border-box;"
                @click.stop="markAsRead(String(item.id))"
              >
                标为已读
              </view>
            </view>
          </template>
        </wd-swipe-action>
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "消息通知"
  }
}
</route>

<style scoped lang="scss">
.message-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f4f5;
}

:deep(.message-tabs) {
  background-color: #ffffff;
  border-bottom: 0.5px solid #ebeef5;

  .wd-tabs__nav-item {
    color: #303133;
    font-size: 15px;
  }
  .wd-tabs__nav-item.is-active {
    color: #409eff;
  }
  .wd-tabs__line {
    background-color: #409eff;
    height: 2px;
  }
}

.read-all-section {
  background-color: #ffffff;
  padding: 8px 15px;
  border-bottom: 0.5px solid #ebeef5;
}

.read-all-button {
  font-size: 14px;
  color: #409eff;
}

.list-paging-component {
  flex: 1;
}

.message-item {
  display: flex;
  padding: 12px 15px;
  background-color: #ffffff;
  border-bottom: 0.5px solid #ebeef5;
  align-items: center;

  &__status {
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
  }

  .unread-dot {
    width: 8px;
    height: 8px;
    background-color: #f56c6c;
    border-radius: 50%;
  }

  .read-dot {
    width: 8px;
    height: 8px;
    background-color: #39e380;
    border-radius: 50%;
  }

  .read-icon {
    width: 16px;
    height: 16px;
    border: 1px dashed #909399;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    &__text {
      font-size: 10px;
      color: #909399;
    }
  }

  &__content {
    flex: 1;
    margin-right: 10px;
    overflow: hidden;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  &__title {
    font-size: 13px;
    color: #303133;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 100px);
  }

  &__time {
    font-size: 12px;
    color: #606266;
    white-space: nowrap;
    margin-left: 8px;
    flex-shrink: 0;
  }

  &__body {
    font-size: 12px;
    color: #909399;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
  }

  &__action {
    flex-shrink: 0;
    .view-detail-text {
      font-size: 13px;
      color: #409eff;
    }
  }
}
</style>
