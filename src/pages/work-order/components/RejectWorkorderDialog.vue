<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useToast } from 'wot-design-uni'
import type { WorkOrderRejectReq } from '@/types/api/Workorder'
import DynamicForm from '@/components/DynamicForm.vue'
import DialogBox from '@/components/DialogBox.vue'
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import { auditRejectWorkOrder } from '@/api/workorder'
import { useDictStore } from '@/store/modules/dict'

const props = defineProps<{
  orderCode: string
}>()

const emit = defineEmits(['success'])

const dictStore = useDictStore()
const toast = useToast()
const formRef = ref()
const dialogRef = ref()

// 驳回工单表单数据
const rejectFormData = reactive<WorkOrderRejectReq>({
  orderCode: '',
  rejectReason: '',
  rejectDesc: ''
})

// 表单配置
const formConfig: FormItemConfig[] = [
  {
    type: 'select' as const,
    field: 'rejectReason',
    label: '驳回原因',
    required: true,
    options: dictStore.getDictByType('audit_reject_reason'),
    rules: [{ required: true, message: '请选择驳回原因', trigger: 'change' }]
  },
  {
    type: 'textarea' as const,
    field: 'rejectDesc',
    label: '驳回描述',
    attrs: {
      maxlength: 500,
      placeholder: '请输入驳回工单的详细原因'
    },
  }
]

// 显示驳回表单
const showRejectForm = async () => {
  // 设置当前工单编号
  rejectFormData.orderCode = props.orderCode
  rejectFormData.rejectReason = ''
  rejectFormData.rejectDesc = ''

  // 打开对话框
  dialogRef.value.open()
}

// 确认驳回工单
const handleConfirm = async () => {
  try {
    // 使用表单校验
    const valid = await formRef.value.validate()
    if (valid) {
      toast.loading('正在关闭工单...')
      await auditRejectWorkOrder({
        orderCode: props.orderCode,
        rejectReason: rejectFormData.rejectReason,
        rejectDesc: rejectFormData.rejectDesc
      })
      toast.success('工单驳回成功')
      emit('success') // 发出成功事件
      dialogRef.value.close() // 关闭对话框
    }
  } finally {
    toast.close()
  }
}

// 暴露方法给父组件
defineExpose({
  showRejectForm
})
</script>

<template>
  <DialogBox ref="dialogRef" selector="reject-workorder-dialog" title="驳回工单" cancelText="取消" confirmText="确认驳回"
    @confirm="handleConfirm">
    <DynamicForm ref="formRef" :config="formConfig" v-model="rejectFormData" labelPosition="top" />
  </DialogBox>
</template>
