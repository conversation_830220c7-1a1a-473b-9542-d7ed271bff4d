<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import type {
  WorkOrderSubmitReq,
  WorkOrderConfig,
  WorkorderConfigPageParams,
} from '@/types/api/Workorder'
import { submitWorkOrder, getWorkOrderConfigList } from '@/api/workorder'
import DynamicForm from '@/components/DynamicForm.vue'
import { useToast } from 'wot-design-uni'
import type { FormItemConfig } from '@/components/DynamicForm.vue'

interface props extends WorkOrderSubmitReq {
  photos: any
}

const toast = useToast()

const formData = ref<props>({
  stationCode: '',
  orderSource: 'MANUAL_REPORT',
  problemDesc: '',
  photos: '',
  configId: undefined,
})

const formRef = ref<any>(null)

const faultCategoryOptions = ref<{ label: string; value: number | string | undefined }[]>([])

const fetchFaultCategories = async () => {
  const params: WorkorderConfigPageParams = {
    orderType: 'report',
  }
  const result = await getWorkOrderConfigList(params)

  faultCategoryOptions.value = result.map((item: WorkOrderConfig) => ({
    value: item.id,
    label: item.configName || '',
  }))
}

onLoad(() => {
  fetchFaultCategories()
})

const formConfig = computed<FormItemConfig[]>(() => [
  {
    type: 'input',
    field: 'stationCode',
    label: '电站编码',
    required: true,
    readonly: true,
    placeholder: '请先选择电站编码',
    attrs: {
      rightSlot: true,
    },
  },
  {
    type: 'select',
    field: 'configId',
    label: '故障现象',
    required: true,
    options: faultCategoryOptions.value,
    attrs: {
      type: 'radio',
    },
  },
  {
    type: 'upload',
    field: 'photos',
    label: '上传附件',
    required: true,
    attrs: {
      maxCount: 5,
      hint: '· 限制上传 5 个图片',
      sourceType: ['camera','album']
    },
  },
  {
    type: 'textarea',
    field: 'problemDesc',
    label: '问题描述',
    attrs: {
      maxlength: 500,
      showWordLimit: true,
      customStyle: 'width: 100%',
    },
  },
])

function handleSelectStation() {
  uni.navigateTo({
    url: '/pages/station/select',
    events: {
      selectStation: (data: { stationCode: string }) => {
        if (data && data.stationCode) {
          formData.value.stationCode = data.stationCode
        }
      },
    },
  })
}

// 辅助函数：获取最新工单数据、发送事件并返回
const emitUpdateAndNavigateBack = async (customMessage?: string) => {
  let eventChannel: UniApp.EventChannel | undefined
  const instance = getCurrentInstance()?.proxy as any
  eventChannel = instance?.getOpenerEventChannel()

  try {
    if (eventChannel && eventChannel.emit) {
      eventChannel.emit('workOrderUpdated', {
        needsRefresh: true,
      })
    }
    toast.success(customMessage || '操作成功')
  } finally {
    uni.navigateBack()
  }
}

const isSubmitting = ref(false)

async function handleSubmit() {
  if (isSubmitting.value) return
  if (!formRef.value) return

  const valid = await formRef.value.validateWithHandleError()

  if (valid) {
    isSubmitting.value = true
    const formValues = formData.value
    const payload: WorkOrderSubmitReq = {
      stationCode: formValues.stationCode,
      orderSource: formValues.orderSource,
      problemDesc: formValues.problemDesc,
      photos: formValues.photos.map((item: any) => item.url).join(','),
    }

    if (formValues.configId) {
      payload.configId = formValues.configId
    }

    try {
      await submitWorkOrder(payload)
      emitUpdateAndNavigateBack()
    } catch (error) {
    } finally {
      isSubmitting.value = false
    }
  }
}
</script>

<template>
  <view class="page-container">
    <view class="form-card">
      <DynamicForm ref="formRef" v-model="formData" :config="formConfig">
        <template #right-stationCode>
          <wd-button
            custom-class="!min-w-16 !ml-2"
            type="primary"
            :round="false"
            @click="handleSelectStation"
          >
            选择
          </wd-button>
        </template>
      </DynamicForm>
    </view>
    <view class="submit-button-wrap">
      <wd-button type="primary" block :round="false" :loading="isSubmitting" @click="handleSubmit">
        提交工单
      </wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "提报工单"
  }
}
</route>

<style scoped lang="scss">
.page-container {
  background-color: #f5f5f5;
  padding: 24rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  min-height: 100vh;
  box-sizing: border-box;
}

.form-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.submit-button-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px calc(12px + env(safe-area-inset-bottom));
  background: #fff;
  border-top: 1px solid #f2f2f2;
  z-index: 10;
  box-sizing: border-box;
}
</style>
