<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { baseChartOption } from '@/plugins/echarts'
import type { EChartsOption } from 'echarts/types/dist/shared'
import { getInverterDataList, getInverterElecData, getInverterMpptDataList } from '@/api/station'
import type {
  StationInverterRealtimeData,
  StationInverterPowerData,
  InverterMpptData,
} from '@/types/api/Station'
import EchartWrapper from '@/components/charts/EchartWrapper.vue'

const inverterSn = ref('')

const chartData = ref<StationInverterRealtimeData[]>([])
const elecData = ref<StationInverterPowerData>({})
const mpptData = ref<InverterMpptData>({
  mpptCount: 0,
  mppts: [],
})

const chartLoading = ref(true)

const inverterStateText = computed(() => {
  const state = elecData.value.inveterState
  if (state === 1)
    return '在线'
  if (state === 2)
    return '离线'
  if (state === 3)
    return '报警'
  return '-'
})

function deepMerge(target: any, source: any) {
  const result = { ...target }
  if (typeof source !== 'object' || source === null) {
    return result
  }
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      const targetValue = result[key]
      const sourceValue = source[key]
      if (typeof sourceValue === 'object' && sourceValue !== null && !Array.isArray(sourceValue) && typeof targetValue === 'object' && targetValue !== null && !Array.isArray(targetValue)) {
        result[key] = deepMerge(targetValue, sourceValue)
      }
      else {
        result[key] = sourceValue
      }
    }
  }
  return result
}

const chartOption = computed<EChartsOption>(() => {
  const dynamicOptions: EChartsOption = {
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.value.map(d => (d.dataTimestamp ? dayjs(d.dataTimestamp).format('HH:mm') : '')),
    },
    yAxis: {
      type: 'value',
      name: '功率(kW)',
    },
    series: [
      {
        name: '发电功率',
        type: 'line',
        smooth: true,
        data: chartData.value.map(d => d.pac ?? 0),
        areaStyle: {},
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        type: 'slider',
        height: 20,
        bottom: 10,
        start: 0,
        end: 100,
        showDetail: false,
      },
    ],
  }
  return deepMerge(baseChartOption, dynamicOptions)
})

async function fetchData() {
  if (!inverterSn.value) return
  chartLoading.value = true
  try {
    const today = dayjs().format('YYYY-MM-DD')
    const chartRes = await getInverterDataList({ inverterSn: inverterSn.value, date: today })
    if (chartRes && chartRes.length > 0) {
      chartData.value = chartRes
    }
    else {
      chartData.value = []
    }
    const elecRes = await getInverterElecData({ inverterSn: inverterSn.value })
    if (elecRes) {
      elecData.value = elecRes
    }
    const mpptRes = await getInverterMpptDataList({ inverterSn: inverterSn.value, date: today })
    if (mpptRes && mpptRes.mppts) {
      mpptData.value = mpptRes
    }
    else {
      mpptData.value = { mpptCount: 0, mppts: [] }
    }
  }
  catch (error) {
    console.error('Failed to fetch inverter data:', error)
    chartData.value = []
    elecData.value = {}
    mpptData.value = { mpptCount: 0, mppts: [] }
  }
  finally {
    chartLoading.value = false
  }
}

onLoad((options) => {
  if (options?.inverterSn) {
    inverterSn.value = options.inverterSn
    fetchData()
  }
})
</script>

<template>
  <view class="inverter-detail-page">
    <scroll-view scroll-y class="page-content">
      <view class="card">
        <view class="card__header">
          <view class="card__title-decorator"></view>
          <text class="card__title-text">实时状态</text>
        </view>
        <view class="card__body">
          <view class="info-row">
            <text class="info-row__label">实时功率(kW)</text>
            <text class="info-row__value">{{ elecData.pac ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">逆变器状态</text>
            <text class="info-row__value">{{ inverterStateText }}</text>
          </view>
        </view>
      </view>
      <view class="card">
        <view class="card__header">
          <view class="card__title-decorator"></view>
          <text class="card__title-text">发电量统计</text>
        </view>
        <view class="card__body">
          <view class="info-row">
            <text class="info-row__label">日发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecDay ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">月发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecMonth ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">年发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecYear ?? '-' }}</text>
          </view>
          <view class="info-row">
            <text class="info-row__label">累计发电量(kWh)</text>
            <text class="info-row__value">{{ elecData.elecTotal ?? '-' }}</text>
          </view>
        </view>
      </view>
      <view class="card chart-card">
        <view class="card__header">
          <view class="card__title-decorator"></view>
          <text class="card__title-text">发电功率</text>
        </view>
        <view class="card__body">
          <EchartWrapper v-if="chartData.length > 0" :options="chartOption" :loading="chartLoading" height="240px" />
          <view v-else class="empty-data">
            <text v-if="chartLoading">加载中...</text>
            <text v-else>暂无数据</text>
          </view>
        </view>
      </view>
      <template v-if="mpptData.mppts && mpptData.mppts.length > 0">
        <view v-for="(mppt, index) in mpptData.mppts" :key="index" class="card mppt-info-card">
          <view class="card__header">
            <view class="card__title-decorator"></view>
            <text class="card__title-text">MPPT{{ index + 1 }}</text>
          </view>
          <view class="card__body">
            <view class="info-row">
              <text class="info-row__label">电压(V)</text>
              <text class="info-row__value">{{ mppt.u || '-' }}</text>
            </view>
            <view class="info-row">
              <text class="info-row__label">电流(A)</text>
              <text class="info-row__value">{{ mppt.i || '-' }}</text>
            </view>
          </view>
        </view>
      </template>
      <view v-else-if="!chartLoading" class="card">
        <view class="card__header">
          <view class="card__title-decorator"></view>
          <text class="card__title-text">MPPT</text>
        </view>
        <view class="card__body empty-data">暂无数据</view>
      </view>
    </scroll-view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "逆变器详情"
  }
}
</route>

<style scoped lang="scss">
.inverter-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f5;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  box-sizing: border-box;
}

.card {
  background-color: #ffffff;
  border-radius: 6px;
  margin-bottom: 15px;
  padding: 17px 18px;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  &__title-decorator {
    width: 3px;
    height: 15px;
    background-color: #37acfe;
    margin-right: 8px;
  }

  &__title-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #4b4b4b;
  }

  &__body {
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #91929e;
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 15px;
    color: #4b4b4b;
    text-align: right;
  }
}

.mppt-info-card {
  .info-row {
    min-height: 24px;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.chart-card {
  padding-bottom: 10px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 240px;
  color: #909399;
  font-size: 14px;
}
</style>
