// @/api/station.ts
import type { PaginatedContent } from '@/service/types'
import type {
  GetStationInverterDataParams,
  GetStationInverterElecDataParams,
  GetStationInverterListParams,
  GetStationInverterMpptDataParams,
  Inverter,
  InverterMpptData,
  Station,
  StationGetByStationCodeParams,
  StationInverterPowerData,
  StationInverterRealtimeData,
  StationPageParams,
} from '@/types/api/Station'
import { getInstance } from '@/service'
import { useUserStore } from '@/store/modules/user'

/**
 * 分页查询电站列表
 */
export async function getStationPage(
  params: StationPageParams,
): Promise<PaginatedContent<Station>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/page',
    merchant: '/light/operation/station/page',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}

/**
 * @description 获取逆变器实时数据列表
 * @param params
 * @returns
 */
export async function getInverterDataList(
  params: GetStationInverterDataParams,
): Promise<StationInverterRealtimeData[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/inverter/data',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}

/**
 * @description 获取逆变器发电量数据
 * @param params
 * @returns
 */
export async function getInverterElecData(
  params: GetStationInverterElecDataParams,
): Promise<StationInverterPowerData> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/inverter/elec-data',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}

/**
 * @description 获取电站逆变器列表
 * @param params
 * @returns
 */
export async function getInverterList(params: GetStationInverterListParams): Promise<Inverter[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/inverter/list',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}

/**
 * @description 获取逆变器MPPT数据列表
 * @param params
 * @returns
 */
export async function getInverterMpptDataList(
  params: GetStationInverterMpptDataParams,
): Promise<InverterMpptData> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/inverter/mppt-data',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}

/**
 * 根据电站编码获取电站详情
 */
export async function getStationByStationCode(
  params: StationGetByStationCodeParams,
): Promise<Station> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/getByStationCode',
    merchant: '/light/operation/station/getByStationCode',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}
