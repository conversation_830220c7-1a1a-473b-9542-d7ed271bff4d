// src/api/inspect.ts
import type { PaginatedContent } from '@/service/types'
import type {
  InspectHandleParams,
  InspectionPlan,
  InspectionPlanStatistics,
  InspectionPlanStatisticsTotal,
  InspectionWorkOrder,
  InspectPlanGetParams,
  InspectPlanListParams as InspectPlanListParamsType,
  InspectPlanPageParams,
  InspectWorkOrderGetParams,
  InspectWorkOrderListParams,
  InspectWorkOrderPageParams,
  InspectWorkOrderStatisticsParams,
  InspectWorkOrderStatisticsTotalParams,
} from '@/types/api/Inspect'
import { getInstance, hdsInstance, merchantInstance } from '@/service'
import { useUserStore } from '@/store'

/**
 * 获取巡检计划详情
 */
export async function getInspectionPlanGet(params: InspectPlanGetParams): Promise<InspectionPlan> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = { haier: '/light/operation/inspection/plan/get' }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 巡检计划列表
 */
export async function getInspectionPlanList(
  params?: InspectPlanListParamsType,
): Promise<InspectionPlan[]> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = { haier: '/light/operation/inspection/plan/list' }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 分页查询巡检计划列表
 */
export async function getInspectionPlanPage(
  params?: InspectPlanPageParams,
): Promise<PaginatedContent<InspectionPlan>> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = { haier: '/light/operation/inspection/plan/page' }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 根据工单编号获取巡检工单
 */
export async function getInspectionWorkOrder(
  params: InspectWorkOrderGetParams,
): Promise<InspectionWorkOrder> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = {
    haier: '/light/operation/inspection/work-order/get',
    merchant: '/light/operation/inspection/work-order/get',
  }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 巡检工单列表 (Generic, using getInstance)
 */
export async function getInspectionWorkOrderList(
  params: InspectWorkOrderListParams,
): Promise<InspectionWorkOrder[]> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = {
    haier: '/light/operation/inspection/work-order/list',
    merchant: '/light/operation/inspection/work-order/list',
  }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 分页查询巡检工单列表
 */
export async function getInspectionWorkOrderPage(
  params: InspectWorkOrderPageParams,
): Promise<PaginatedContent<InspectionWorkOrder>> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = {
    haier: '/light/operation/inspection/work-order/page',
    merchant: '/light/operation/inspection/work-order/page',
  }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 巡检计划统计（按运维商、资方）
 */
export async function getInspectionWorkOrderStatisticsPlan(
  params: InspectWorkOrderStatisticsParams,
): Promise<PaginatedContent<InspectionPlanStatistics>> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = {
    haier: '/light/operation/inspection/work-order/statistics/plan',
    merchant: '/light/operation/inspection/work-order/statistics',
  }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 巡检计划统计（按运维商、资方）
 */
export async function getInspectionWorkOrderStatistics(
  params: InspectWorkOrderStatisticsParams,
): Promise<PaginatedContent<InspectionPlanStatistics>> {
  return await hdsInstance.get('/light/operation/inspection/work-order/statistics', { params })
}

/**
 * InspectionPlanStatisticsTotal
 */
export async function getInspectionWorkOrderStatisticsTotal(
  params: InspectWorkOrderStatisticsTotalParams,
): Promise<InspectionPlanStatisticsTotal> {
  const userStore = useUserStore()
  const currentUserType = userStore.userInfo?.userType

  if (!currentUserType) {
    throw new Error('User type is not defined.')
  }
  const urlMap: Record<string, string> = {
    haier: '/light/operation/inspection/work-order/statistics/total',
    merchant: '/light/operation/inspection/work-order/statistics/total',
  }
  const path = urlMap[currentUserType]

  if (!path) {
    throw new Error(`No path defined for user type "${currentUserType}" in this API function.`)
  }
  return await getInstance().get(path, { params })
}

/**
 * 处理巡检工单
 * @summary 处理巡检工单
 * @operationId handleWorkOrderUsingPOST
 */
export async function handleInspectionWorkOrder(data: InspectHandleParams): Promise<boolean> {
  return await merchantInstance.post('/light/operation/inspection/work-order/handle', data);
}
