import type { PaginatedContent } from '@/service/types'
import type {
  BatchAuditResultDto,
  WorkOrder,
  WorkorderBatchAuditPassParams,
  WorkorderBatchDispatchParams,
  WorkOrderCloseReq,
  WorkOrderConfig,
  WorkorderConfigPageParams,
  WorkOrderHandleReq,
  WorkOrderPageParams,
  WorkOrderRejectReq,
  WorkOrderSubmitReq,
} from '@/types/api/Workorder'
import { getInstance, hdsInstance } from '@/service'
import { useUserStore } from '@/store'

export async function getWorkOrderPage(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/page',
    haier: '/light/operation/workOrder/page',
  }
  const path = urlMap[userType]

  return await getInstance().get(path, { params })
}

export async function auditRejectWorkOrder(data: WorkOrderRejectReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/audit-reject',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function batchAuditPassWorkOrder(
  data: WorkorderBatchAuditPassParams,
): Promise<BatchAuditResultDto> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/batch-audit-pass',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function batchDispatchWorkOrder(
  data: WorkorderBatchDispatchParams,
): Promise<BatchAuditResultDto> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/batch-dispatch',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function closeWorkOrder(data: WorkOrderCloseReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/close',
    haier: '/light/operation/workOrder/close',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderByOrderCode(orderCode: string): Promise<WorkOrder> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: `/light/operation/workOrder/getByOrderCode/${orderCode}`,
    haier: `/light/operation/workOrder/getByOrderCode/${orderCode}`,
  }
  const path = urlMap[userType]
  return await getInstance().get(path)
}

export async function handleWorkOrder(data: WorkOrderHandleReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/handle',
    haier: '/light/operation/workOrder/handle',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderHandledPage(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/handled/page',
    haier: '/light/operation/workOrder/handled/page',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function getWorkOrderList(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/list',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function getWorkOrderMySubmitted(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/my-submitted',
    haier: '/light/operation/workOrder/my-submitted',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function submitWorkOrder(data: WorkOrderSubmitReq): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '/light/operation/workOrder/submit',
    haier: '/light/operation/workOrder/submit',
  }
  const path = urlMap[userType]
  return await getInstance().post(path, data)
}

export async function getWorkOrderToAudit(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/to-audit',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function getWorkOrderToDispatch(
  params: WorkOrderPageParams,
): Promise<PaginatedContent<WorkOrder>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: '/light/operation/workOrder/to-dispatch',
  }
  const path = urlMap[userType]
  return await getInstance().get(path, { params })
}

export async function auditPassWorkOrder(orderCode: string): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: `/light/operation/workOrder/${orderCode}/audit-pass`,
  }
  const path = urlMap[userType]
  return await getInstance().post(path)
}

export async function getWorkOrderConfigList(
  params: WorkorderConfigPageParams,
): Promise<WorkOrderConfig[]> {
  const path = '/light/operation/work-order-config/list'
  return await hdsInstance.get(path, { params })
}

export async function dispatchWorkOrder(orderCode: string): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo!.userType
  const urlMap: Record<typeof userType, string> = {
    merchant: '',
    haier: `/light/operation/workOrder/${orderCode}/dispatch`,
  }
  const path = urlMap[userType]
  return await getInstance().post(path)
}
