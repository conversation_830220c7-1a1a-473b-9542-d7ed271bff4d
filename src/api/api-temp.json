{"swagger": "2.0", "info": {"description": "外部系统调用网关", "version": "1.0", "title": "日日顺乐农商户平台网关API接口", "contact": {"name": "rrsjk", "url": "http://www.rrsjk.com", "email": "<EMAIL>"}}, "host": "operation.xiaoxianglink.com", "basePath": "/hdsapi", "paths": {"/light/operation/station/inverter/data": {"get": {"tags": ["光伏运维/电站管理"], "summary": "获取逆变器实时数据列表", "operationId": "getInverterDataListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "date", "in": "query", "description": "查询日期", "required": false, "type": "string"}, {"name": "inverterSn", "in": "query", "description": "逆变器SN", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/逆变器实时数据"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/station/inverter/elec-data": {"get": {"tags": ["光伏运维/电站管理"], "summary": "获取逆变器发电量数据", "operationId": "getInverterElecDataUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "inverterSn", "in": "query", "description": "逆变器SN", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/逆变器发电量数据"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/station/inverter/list": {"get": {"tags": ["光伏运维/电站管理"], "summary": "获取电站逆变器列表", "operationId": "getInverterListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "stationCode", "in": "query", "description": "电站编码", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LightOperationInverter"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/station/inverter/mppt-data": {"get": {"tags": ["光伏运维/电站管理"], "summary": "获取逆变器MPPT数据列表", "operationId": "getInverterMpptDataListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "date", "in": "query", "description": "查询日期", "required": false, "type": "string"}, {"name": "inverterSn", "in": "query", "description": "逆变器SN", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/InverterMpptDataDto"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "逆变器发电量数据": {"type": "object", "properties": {"dailyEquivalentHours": {"type": "number", "description": "日等效小时数"}, "dataTimeAt": {"type": "string", "format": "date-time", "description": "数据更新时间"}, "elecDay": {"type": "number", "description": "日发电量(kWh)"}, "elecMonth": {"type": "number", "description": "月发电量(kWh)"}, "elecTotal": {"type": "number", "description": "累计发电量(kWh)"}, "elecYear": {"type": "number", "description": "年发电量(kWh)"}, "inveterState": {"type": "integer", "format": "int32", "description": "逆变器状态，1：在线 2：离线 3：报警"}, "pac": {"type": "number", "description": "实时功率(KW)"}, "power": {"type": "number", "description": "装机容量(KW)"}}, "title": "逆变器发电量数据"}, "逆变器实时数据": {"type": "object", "properties": {"dataTimestamp": {"type": "string", "format": "date-time", "description": "数据时间"}, "inverterTemperature": {"type": "number", "description": "逆变器温度"}, "pac": {"type": "number", "description": "实时功率"}}, "title": "逆变器实时数据"}, "LightOperationInverter": {"type": "object", "properties": {"brandName": {"type": "string"}, "imageUrl": {"type": "string"}, "inverterModel": {"type": "string"}, "inverterSn": {"type": "string"}}, "title": "LightOperationInverter"}, "InverterMpptDataDto": {"type": "object", "properties": {"dataTimestamp": {"type": "string", "format": "date-time", "description": "数据时间"}, "mpptName": {"type": "string", "description": "MPPT名称"}, "power": {"type": "number", "description": "功率"}}, "title": "InverterMpptDataDto", "description": "逆变器MPPT数据"}}